import printingControl from"\u002E\u002F\u0050\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0043\u006F\u006E\u0074\u0072\u006F\u006C\u002E\u006A\u0073";import pr from"\u002E\u002F\u0050\u0041\u0047\u0045\u005F\u0052\u0045\u0047\u005F\u0042\u0049\u0054\u0053\u002E\u006A\u0073";import lzma from"\u002E\u002F\u006C\u007A\u006D\u0061\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import imageDataUtils from"\u002E\u002F\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0055\u0074\u0069\u006C\u0073";import bleToothManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065";import bleTool from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C";import constants from"\u002E\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";import supVanPrintUtilsG21 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";export default{"matWidth":25,'\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068':2,'\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074':30,"dpiValue":8,'\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061':[],"encodeList":[],"overturnType":0,"imageDataListAll":[],"imageDataListAllTotalCnt":0,"printFirstType":false,'\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074':[],"objectData":null,"printType":'','\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B':null,async initEncodeData(objectData,imageRgbaData){try{console['\u006C\u006F\u0067']("\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031",objectData);var _0xe5g5f=(686937^686940)+(718385^718389);const that=this;_0xe5g5f='\u0065\u006C\u006D\u006A\u0063\u0067';that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']=objectData;that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061']=imageRgbaData;that['\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065']=objectData['\u0069\u0073\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E']?821197^821196:966183^966183;that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();await that['\u0069\u006E\u0069\u0074\u0069\u0061\u006C\u0069\u007A\u0065\u0044\u0061\u0074\u0061']();await that['\u0069\u006E\u0069\u0074\u004C\u005A\u004D\u0041\u0044\u0061\u0074\u0061']();await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074']();}catch(error){throw error;}},async initializeData(){var _0x_0x496=(153000^153002)+(758384^758384);const that=this;_0x_0x496=(872769^872769)+(791758^791754);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];printingControl['\u0063\u006C\u0065\u0061\u006E\u0050\u0063']();imageDataUtils['\u0067\u0065\u0074\u0043\u006C\u0065\u0061\u006E']();pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();let _0x117f5d;let length=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068'];_0x117f5d='\u0063\u006C\u006F\u006E\u0070\u0071';that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']=length-(847402^847406);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(527522^527521)){that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']=length-that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']*(559818^559816);}if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']==(939175^939175)){that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']=324024^324025;}var _0x4c_0xc7f=(144958^144956)+(567844^567843);let rotateAngle=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']-(921383^921381))*(299409^299467);_0x4c_0xc7f=(595326^595320)+(487765^487767);let object={'\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061':that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061'],"Width":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"Height":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0072\u006F\u0074\u0061\u0074\u0065\u0041\u006E\u0067\u006C\u0065':rotateAngle,'\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']*(407006^407002),'\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D']*(311209^311213)+1.5*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065':that['\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065'],"DeviceSn":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']};imageDataUtils['\u0067\u0065\u0074\u0041\u006C\u006C\u0042\u0079\u0074\u0065\u0073'](object);that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']=imageDataUtils['\u0067\u0065\u0074\u0057\u0069\u0064\u0074\u0068']();that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']=imageDataUtils['\u0067\u0065\u0074\u0048\u0065\u0069\u0067\u0068\u0074']();printingControl['\u0043\u006F\u006C\u0075\u006D\u006E\u004C\u0065\u0066\u0074']=that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},async initLZMAData(){try{const that=this;let bufferTransferCount=280473^280473;var _0xbcf43c=(925680^925688)+(877974^877983);let num=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0043\u006F\u0070\u0069\u0065\u0073'];_0xbcf43c=(607693^607694)+(903728^903734);let _0xceab;let _bufLength=619237^615141;_0xceab=(499070^499067)+(147933^147924);let countBuff=new Array();let isEndFlag=!![];let _0xed47c;let imgTotalCount=990142^990143;_0xed47c=(682298^682290)+(843873^843881);var _0x78a=(800871^800868)+(359765^359764);let marginleft=imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']();_0x78a=(519938^519936)+(197213^197213);let marginright=imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']();console['\u006C\u006F\u0067']("\u8DDD\u8FB9\u5DE6\u7247\u56FE".split("").reverse().join(""),marginleft);let _nColumnTotalCnt=that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginleft;console['\u006C\u006F\u0067']("\u6570\u5217\u7247\u56FE".split("").reverse().join(""),_nColumnTotalCnt);let nBytePerLine=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(865394^865397))/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);console['\u006C\u006F\u0067']("\u6BCF\u5217\u5B57\u8282\u6570",nBytePerLine);let _0xf2341d;let nMax=Math['\u0066\u006C\u006F\u006F\u0072']((_bufLength-(232671^232649))/nBytePerLine);_0xf2341d=(138420^138416)+(454193^454200);console['\u006C\u006F\u0067']("\u6570\u5217\u5927\u6700\u533A\u51B2\u7F13\u4E2A\u6BCF".split("").reverse().join(""),nMax);var _0xdf3a2a=(407964^407963)+(869593^869597);let bufferCountImage=Math['\u0066\u006C\u006F\u006F\u0072']((_nColumnTotalCnt+nMax-(820704^820705))/nMax);_0xdf3a2a='\u0064\u0062\u006E\u0069\u0063\u006D';console['\u006C\u006F\u0067']("\u6BCF\u4E2A\u56FE\u7247\u7F13\u51B2\u533A\u6570\u91CF\u0020\u0062\u0075\u0066\u0066\u0065\u0072\u0043\u006F\u0075\u006E\u0074\u0049\u006D\u0061\u0067\u0065",bufferCountImage);let allBytes=imageDataUtils['\u0067\u0065\u0074\u0042\u0079\u0074\u0065\u0073\u0041\u006C\u006C']();console['\u006C\u006F\u0067']("\u0061\u006C\u006C\u0062\u0079\u0074\u0065\u0073\u957F\u5EA6",allBytes['\u006C\u0065\u006E\u0067\u0074\u0068']);for(var n=338421^338421;n<imgTotalCount;n++){that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']++;for(var i=906743^906743;i<bufferCountImage;i++){let _btBuf=new Uint8Array(_bufLength);pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();pr['\u0073\u0065\u0074\u0043\u0075\u0074'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0043\u0075\u0074\u0054\u0079\u0070\u0065']);pr['\u0073\u0065\u0074\u004E\u006F\u0064\u0075'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u006E\u0073\u0069\u0074\u0079']);pr['\u0073\u0065\u0074\u004D\u0061\u0074'](119610^119611);pr['\u0073\u0065\u0074\u0053\u0061\u0076\u0065\u0070\u0061\u0070\u0065\u0072'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0053\u0061\u0076\u0065\u0050\u0061\u0070\u0065\u0072']?329725^329724:824781^824781);pr['\u0073\u0065\u0074\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']!==null&&that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']!==undefined?that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']:489911^489911);if(i==(425234^425234)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0053\u0074'](876474^876475);}var _0xae10f=(640447^640441)+(728412^728405);let bufferColumnCnt=838266^838266;_0xae10f=644948^644946;if(i==bufferCountImage-(128743^128742)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0045\u006E\u0064'](402469^402468);if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']==bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()&&isEndFlag){pr['\u0073\u0065\u0074\u0050\u0072\u0074\u0045\u006E\u0064'](267784^267785);}bufferColumnCnt=_nColumnTotalCnt-nMax*i;}else{bufferColumnCnt=nMax;}let star=nMax*i*nBytePerLine+marginleft*nBytePerLine;let _0xb82bf;let end=star+bufferColumnCnt*nBytePerLine;_0xb82bf=825511^825509;let b=allBytes['\u0073\u006C\u0069\u0063\u0065'](star,end);_btBuf[326407^326403]=bufferColumnCnt&(282390^282601);_btBuf[374141^374136]=bufferColumnCnt>>(306761^306753)&(402760^402871);for(var y=523040^523040;y<b['\u006C\u0065\u006E\u0067\u0074\u0068'];y++){_btBuf[(337244^337234)+y]=b[y];}let btdata=pr['\u0074\u006F\u0042\u0079\u0074\u0065\u0041\u0072\u0072\u0061\u0079'](679415^679409);for(var z=724866^724864;z<(798060^798056);z++){_btBuf[z]=btdata[z-(611648^611650)];}_btBuf[184686^184680]=nBytePerLine&(479013^479194);let _0x4705b;let left=741901^741901;_0x4705b=(550923^550914)+(442678^442674);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']!=(871272^871272)){left=281874^281875;}if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(557308^557311)){var _0x3e9gd=(450230^450228)+(311787^311790);let marginLeft=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']+left)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0x3e9gd=(639883^639874)+(412024^412016);let _0xe26dcf;let marginRight=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0xe26dcf="cedcmc".split("").reverse().join("");_btBuf[386667^386659]=marginLeft&(636774^636825);_btBuf[914614^914623]=marginLeft>>(533602^533610)&(331886^331921);_btBuf[802233^802227]=marginRight&(396892^396963);_btBuf[475092^475103]=marginRight>>(902753^902761)&(907542^907753);}else{let marginLeft=((that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']+(974562^974566))*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'])/(858017^858019)+left*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];var _0xb586fc=(635738^635737)+(505585^505593);let marginRight=(that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']+(619978^619982))*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginLeft;_0xb586fc=(697184^697186)+(556115^556123);let margin=Math['\u006D\u0061\u0078'](609786^609790,marginLeft);_btBuf[593131^593123]=margin&(181767^182008);_btBuf[360697^360688]=margin>>(677054^677046)&(371180^370963);margin=Math['\u006D\u0061\u0078'](583119^583115,marginRight);_btBuf[695028^695038]=margin&(363132^363139);_btBuf[565941^565950]=margin>>(536057^536049)&(413496^413639);}_btBuf[772803^772815]=368045^368045;_btBuf[692983^692986]=423281^423281;var _0x4cdb7d=(965139^965146)+(641803^641806);let len=_btBuf[492349^492344];_0x4cdb7d=(370889^370888)+(455095^455095);len<<=839901^839893;len+=_btBuf[232747^232751];len*=_btBuf[435593^435599];len+=738278^738280;let un=986878^986878;for(var j=870774^870772;j<(126070^126072);j++){un+=_btBuf[j];}let x=Math['\u0066\u006C\u006F\u006F\u0072'](len/(693874^694130));if(x>(537252^537252)){for(var k=231147^231147;k<x;k++){un+=_btBuf[(k+(273881^273880))*(384224^384480)-(147466^147467)];}}_btBuf[505217^505217]=un;_btBuf[794521^794520]=un>>(171904^171912);countBuff['\u0070\u0075\u0073\u0068'](_btBuf);console['\u006C\u006F\u0067']("rp".split("").reverse().join(""),pr);}}while(bufferTransferCount<bufferCountImage*num&&countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']>(590561^590561)){let sendData=null;let bufferCount=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);let bufferCountOne=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);do{let _0xc50f;let bufferPackage=Array();_0xc50f=561412^561409;for(let a=494656^494656;a<bufferCount;a++){for(var b=866255^866255;b<_bufLength;b++){bufferPackage[b+a*_bufLength]=countBuff[a][b];}}try{this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=[];this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=bufferPackage;sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](bufferPackage);}catch(error){console['\u006C\u006F\u0067']("\u538B\u7F29\u5931\u8D25");sendData=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];num=488889^488889;throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0034'],error);}bufferCount--;}while(sendData['\u004C\u0065\u006E\u0067\u0074\u0068']>_bufLength);bufferTransferCount=bufferTransferCount+bufferCount+(157883^157882);countBuff['\u0073\u0070\u006C\u0069\u0063\u0065'](511586^511586,bufferCountOne);if(sendData['\u006C\u0065\u006E\u0067\u0074\u0068']>(958572^958572)){that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](sendData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](sendData);}}}catch(error){throw error;}},async doPrint(){let _0x89c7e;const that=this;_0x89c7e=(471735^471732)+(254883^254881);console['\u006C\u006F\u0067']("\u5EA6\u957FllAtsiLataDegami".split("").reverse().join(""),that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']);console['\u006C\u006F\u0067']("\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065\u002E\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D\u0028\u0029\u957F\u5EA6",bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']());if(!that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']){that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=!![];if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']!=(889492^889492)){supVanPrintUtilsG21['\u0064\u006F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']);}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0035'],"\u6253\u5370\u5B57\u6A21\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"));}}else{supVanPrintUtilsG21['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0050\u0061\u0067\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']);}},cleanImageDataListAll(){this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']=875136^875136;this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=false;},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;}};
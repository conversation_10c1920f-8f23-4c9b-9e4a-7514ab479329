import supPrintUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import supPrintUtilsMP50 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import supPrintUtilsG15 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import supPrintUtilsG21 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";import canvasDataRGBAUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0061\u0074\u0061\u0052\u0047\u0042\u0041\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import lpapi from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u004C\u0050\u0041\u0050\u0049\u002F\u004C\u0050\u0041\u0050\u0049\u002E\u006A\u0073";export default{'\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041':0x11,'\u0043\u004D\u0044\u005F\u0052\u0045\u0041\u0044\u005F\u0044\u0050\u0049':0x22,"SUPPORT_SERVICE_UUID":["7EEF".split("").reverse().join(""),"FF0E".split("").reverse().join("")],"SERVICE_UUID":"E0FF",'\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044':"\u0046\u0046\u0045\u0031",'\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044':"FFE9",'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':'',"serviceId":'',"notifyId":'','\u0077\u0072\u0069\u0074\u0065\u0049\u0064':'','\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074':[],'\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065':false,"deviceSn":'',"dpiValue":8,wxPromise(api,options={}){return new Promise((resolve,reject)=>{api({...options,'\u0073\u0075\u0063\u0063\u0065\u0073\u0073':res=>resolve(res),'\u0066\u0061\u0069\u006C':e=>reject(e)});});},async scanBleDeviceList(callBack){var _0xb6fggc=(486529^486534)+(242377^242368);const that=this;_0xb6fggc=(135849^135850)+(521760^521765);that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']=[];var _0xg4cc5d=(682899^682907)+(179806^179804);let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0031'];_0xg4cc5d=352654^352652;try{await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u006F\u0070\u0065\u006E\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0041\u0064\u0061\u0070\u0074\u0065\u0072']);resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0032'];var _0x3fcf1c=(921281^921289)+(277837^277835);const stateRes=await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0041\u0064\u0061\u0070\u0074\u0065\u0072\u0053\u0074\u0061\u0074\u0065']);_0x3fcf1c=665291^665289;resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0033'];if(!stateRes&&!stateRes['\u0061\u0076\u0061\u0069\u006C\u0061\u0062\u006C\u0065']){resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0035'];return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,"\u7528\u53EF\u4E0D\u5668\u914D\u9002\u7259\u84DD".split("").reverse().join(""));}await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0074\u0061\u0072\u0074\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0073\u0044\u0069\u0073\u0063\u006F\u0076\u0065\u0072\u0079'],{'\u0061\u006C\u006C\u006F\u0077\u0044\u0075\u0070\u006C\u0069\u0063\u0061\u0074\u0065\u0073\u004B\u0065\u0079':!![]});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0034'];wx['\u006F\u006E\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0046\u006F\u0075\u006E\u0064'](res=>{if(res&&res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][512311^512311]&&res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][473829^473829]['\u006E\u0061\u006D\u0065']&&that['\u0061\u006C\u006C\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][692396^692396]['\u006E\u0061\u006D\u0065'])){if(!that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']['\u0069\u006E\u0063\u006C\u0075\u0064\u0065\u0073'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][382144^382144]['\u006E\u0061\u006D\u0065'])){that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][602900^602900]['\u006E\u0061\u006D\u0065']);callBack(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](res));}}});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async stopScanBleDevices(){try{await this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0074\u006F\u0070\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0073\u0044\u0069\u0073\u0063\u006F\u0076\u0065\u0072\u0079']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':'停止搜索蓝牙设备成功'});}catch(error){var _0x949ae=(712171^712171)+(510209^510214);let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0037'];_0x949ae=(275175^275172)+(414389^414397);throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async disconnectBleDevice(){const that=this;try{if(that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']){if(that['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await lpapi['\u0063\u006C\u006F\u0073\u0065\u0050\u0072\u0069\u006E\u0074\u0065\u0072']();return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':"\u65AD\u5F00\u84DD\u7259\u8BBE\u5907\u6210\u529F"});}else{await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u006C\u006F\u0073\u0065\u0042\u004C\u0045\u0043\u006F\u006E\u006E\u0065\u0063\u0074\u0069\u006F\u006E'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':that['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':'断开蓝牙设备成功'});}}else{let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0037'];return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,"\u84DD\u7259\u8BBE\u5907\u53F7\u4E3A\u7A7A");}}catch(error){console['\u006C\u006F\u0067'](error);var _0xb6e=(941015^941008)+(576282^576281);let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0036'];_0xb6e='\u0069\u0068\u006E\u0066\u006D\u006F';throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async connectBleDevice(nBleDeviceInfo){try{const that=this;let bleDeviceInfo=nBleDeviceInfo;if(bleDeviceInfo){that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']=bleDeviceInfo['\u006E\u0061\u006D\u0065'];if(that['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){if(bleDeviceInfo['\u006E\u0061\u006D\u0065']){await this['\u0063\u006F\u006E\u006E\u0065\u0063\u0074\u0044\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065'](bleDeviceInfo['\u006E\u0061\u006D\u0065']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({"msg":'连接德佟蓝牙设备成功'});}}else{if(bleDeviceInfo['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']){await this['\u0063\u006F\u006E\u006E\u0065\u0063\u0074\u0053\u0075\u0070\u0076\u0061\u006E\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065'](bleDeviceInfo['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':'连接硕方蓝牙设备成功','\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0055\u0075\u0069\u0064':this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']});}}}}catch(error){throw error;}},async connectDtBleDevice(nBluetoothName){var _0x7b3dc;let bluetoothName=nBluetoothName;_0x7b3dc=651816^651823;return new Promise((resolve,reject)=>{lpapi['\u006F\u0070\u0065\u006E\u0050\u0072\u0069\u006E\u0074\u0065\u0072'](bluetoothName,res=>{resolve(res);},error=>{reject(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0039'],error));});});},async connectSupvanBleDevice(nDeviceId){let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0030'];try{const that=this;var _0xa6b=(705673^705672)+(790414^790407);let deviceId=nDeviceId;_0xa6b=(312848^312854)+(917116^917112);that['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']=deviceId;that['\u0073\u0065\u0074\u0050\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C'](wx['\u0067\u0065\u0074\u0053\u0079\u0073\u0074\u0065\u006D\u0049\u006E\u0066\u006F\u0053\u0079\u006E\u0063']()['\u006D\u006F\u0064\u0065\u006C']);await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0072\u0065\u0061\u0074\u0065\u0042\u004C\u0045\u0043\u006F\u006E\u006E\u0065\u0063\u0074\u0069\u006F\u006E'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0031'];that['\u0062\u006C\u0065\u004D\u0074\u0075']();await that['\u0073\u0074\u006F\u0070\u0053\u0063\u0061\u006E\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073']();resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0032'];var _0x07429b=(160717^160716)+(912302^912302);const bleDeviceServicesRes=await that['\u0067\u0065\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u0065\u0072\u0076\u0069\u0063\u0065\u0073']();_0x07429b=(935566^935565)+(328421^328417);resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0033'];bleDeviceServicesRes['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0073']['\u006D\u0061\u0070'](i=>{for(const s of this['\u0053\u0055\u0050\u0050\u004F\u0052\u0054\u005F\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']){if(i&&i['\u0075\u0075\u0069\u0064']&&i['\u0075\u0075\u0069\u0064']['\u006C\u0065\u006E\u0067\u0074\u0068']>=(233552^233560)){if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](615242^615246,199633^199641)==s){this['\u0075\u0070\u0064\u0061\u0074\u0061\u0042\u006C\u0065\u0055\u0075\u0069\u0064'](s);this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}}}});var _0xbcea7d;const bleDeviceCharacteristicsRes=await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u004C\u0045\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0073'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],"serviceId":this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']});_0xbcea7d=(301199^301199)+(240200^240206);resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0034'];bleDeviceCharacteristicsRes['\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0073']['\u006D\u0061\u0070'](i=>{if(i&&i['\u0075\u0075\u0069\u0064']&&i['\u0075\u0075\u0069\u0064']['\u006C\u0065\u006E\u0067\u0074\u0068']>=(570859^570851)){if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](558816^558820,229367^229375)==this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']){this['\u006E\u006F\u0074\u0069\u0066\u0079\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}else if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](505771^505775,666543^666535)==this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']){this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}}});await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u006E\u006F\u0074\u0069\u0066\u0079\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065\u0043\u0068\u0061\u006E\u0067\u0065'],{'\u0073\u0074\u0061\u0074\u0065':!![],"deviceId":this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],'\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064'],"characteristicId":this['\u006E\u006F\u0074\u0069\u0066\u0079\u0049\u0064']});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0035'];wx['\u006F\u006E\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065\u0043\u0068\u0061\u006E\u0067\u0065'](res=>{if(that['\u0074\u0035\u0030\u0050\u0072\u006F\u0041\u006E\u0064\u0054\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtils['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsMP50['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsG15['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsG21['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},updataBleUuid(currentServiceUuid){switch(currentServiceUuid){case"\u0046\u0045\u0045\u0037":this['\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']="7EEF".split("").reverse().join("");this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']="\u0046\u0045\u0043\u0031";this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']="\u0046\u0045\u0043\u0031";break;case"FF0E".split("").reverse().join(""):this['\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']="\u0045\u0030\u0046\u0046";this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']="\u0046\u0046\u0045\u0031";this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']="9EFF".split("").reverse().join("");break;default:break;}},async stopPrint(callback){try{var _0xc9b2b;const that=this;_0xc9b2b=508695^508694;if(this['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](!![]);}else{canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](false);supPrintUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsMP50['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsMP50['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsG15['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsG15['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsG21['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsG21['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);}}catch(error){throw error;}},async bleMtu(){if(wx['\u0067\u0065\u0074\u0053\u0079\u0073\u0074\u0065\u006D\u0049\u006E\u0066\u006F\u0053\u0079\u006E\u0063']()['\u0070\u006C\u0061\u0074\u0066\u006F\u0072\u006D']=="diordna".split("").reverse().join("")){await new Promise(resolve=>setTimeout(resolve,933467^933299));return this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0065\u0074\u0042\u004C\u0045\u004D\u0054\u0055'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],"mtu":182});}return Promise['\u0072\u0065\u0073\u006F\u006C\u0076\u0065'];},async getBleDeviceServices(){await new Promise(resolve=>setTimeout(resolve,587225^585733));return this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u004C\u0045\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u0065\u0072\u0076\u0069\u0063\u0065\u0073'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});},onWriteBLECharacteristicValue(nbyteData){var _0x7fb99b=(475444^475447)+(608011^608002);let byteData=nbyteData;_0x7fb99b=(854541^854536)+(362511^362505);console['\u006C\u006F\u0067'](":\u636E\u6570\u9001\u53D1".split("").reverse().join(""),byteData);if(byteData){var _0x96c=(841126^841125)+(175453^175450);const buffer=new ArrayBuffer(byteData['\u006C\u0065\u006E\u0067\u0074\u0068']);_0x96c=480962^480966;const dataView=new DataView(buffer);for(var i=377120^377120;i<byteData['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){dataView['\u0073\u0065\u0074\u0055\u0069\u006E\u0074\u0038'](i,byteData[i]);}wx['\u0077\u0072\u0069\u0074\u0065\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065']({"deviceId":this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],'\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064'],'\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0049\u0064':this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064'],'\u0076\u0061\u006C\u0075\u0065':dataView['\u0062\u0075\u0066\u0066\u0065\u0072'],'\u0073\u0075\u0063\u0063\u0065\u0073\u0073':res=>{},"fail":res=>{console['\u006C\u006F\u0067']("liaf eulaVcitsiretcarahCELBetirw".split("").reverse().join(""),res);}});}},t50ProBleDevices(nBluetoothName){var _0xaf97fc=(453802^453804)+(603892^603889);const bluetoothName=nBluetoothName;_0xaf97fc=429709^429701;var _0x1633df=(189445^189446)+(696654^696655);const validPrefixes=["\u0054\u0030\u0031\u0030\u0030\u0041","\u0054\u0030\u0030\u0039\u0039\u0041","\u0054\u0030\u0030\u0034\u0036\u0041","\u0054\u0030\u0030\u0031\u0033\u0042","B0200T".split("").reverse().join(""),"B4200T".split("").reverse().join(""),"A7900T".split("").reverse().join(""),"A1010T".split("").reverse().join(""),"A4110T".split("").reverse().join(""),"\u0054\u0030\u0031\u0031\u0035\u0041","A2110T".split("").reverse().join(""),"\u0054\u0030\u0031\u0031\u0033\u0041","\u0054\u0030\u0031\u0034\u0035\u0042","B6410T".split("").reverse().join(""),"\u0054\u0030\u0031\u0034\u0039\u0042","\u0054\u0030\u0031\u0035\u0030\u0042","B1510T".split("").reverse().join(""),"\u0054\u0030\u0031\u0035\u0032\u0042","\u0054\u0030\u0031\u0035\u0033\u0042","B4510T".split("").reverse().join(""),"\u0054\u0030\u0031\u0035\u0035\u0042","\u0054\u0030\u0031\u0035\u0036\u0042","B3010T".split("").reverse().join(""),"\u0054\u0030\u0030\u0032\u0031\u0042","B9510T".split("").reverse().join(""),"\u0054\u0030\u0031\u0036\u0030\u0042","B1610T".split("").reverse().join(""),"B7610T".split("").reverse().join(""),"\u0054\u0030\u0031\u0036\u0038\u0042","B9610T".split("").reverse().join(""),"B2610T".split("").reverse().join(""),"B3610T".split("").reverse().join(""),"\u0054\u0030\u0031\u0038\u0035\u0042"];_0x1633df='\u0064\u006B\u0070\u0067\u0068\u0063';if(bluetoothName){return validPrefixes['\u0073\u006F\u006D\u0065'](prefix=>bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068'](prefix));}return false;},t80BleDevices(nBluetoothName){var _0xgade;let bluetoothName=nBluetoothName;_0xgade=(484243^484241)+(447114^447113);if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0030\u0033\u0033\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0031\u0031\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A9010T".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A5710T".split("").reverse().join(""))){return!![];}else{return false;}}},t50ProAndT80BleDevices(nBluetoothName){var _0xaa_0x29f;let bluetoothName=nBluetoothName;_0xaa_0x29f='\u006F\u006D\u0066\u006F\u006A\u0065';if(this['\u0074\u0035\u0030\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}},mp50BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3000PM".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A4000PM".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0035\u0041")){return!![];}else{return false;}}},dtBleDevices(nBluetoothName){var _0x59277f;let bluetoothName=nBluetoothName;_0x59277f=(432516^432517)+(392879^392877);if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0035\u0030")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0038\u0030")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("05PM".split("").reverse().join(""))){return!![];}else{return false;}}},g15BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0033\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("4100G".split("").reverse().join(""))){return!![];}else{return false;}},e11BleDevices(nBluetoothName){var _0x3a53d;let bluetoothName=nBluetoothName;_0x3a53d=(353471^353464)+(663633^663640);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0033\u0038\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A9310T".split("").reverse().join(""))){return!![];}else{return false;}},g11BleDevices(nBluetoothName){var _0x6f663g=(322078^322071)+(155318^155319);let bluetoothName=nBluetoothName;_0x6f663g=(962806^962805)+(820965^820960);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0032\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0031\u0041")){return!![];}else{return false;}},g11ProBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0038\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0038\u0042")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0035\u0041")){return!![];}else{return false;}},g11MBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0033\u0041")){return!![];}else{return false;}},g11MPlusBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0035\u0041")){return!![];}else{return false;}},g15MBleDevices(nBluetoothName){var _0xeb_0x30e=(281511^281510)+(433697^433703);let bluetoothName=nBluetoothName;_0xeb_0x30e='\u0070\u006D\u006E\u0069\u006B\u0063';if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("4000G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0037\u0041")){return!![];}else{return false;}},g15MProBleDevices(nBluetoothName){var _0xgc2e6a;let bluetoothName=nBluetoothName;_0xgc2e6a="naehek".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0038\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("B8100G".split("").reverse().join(""))){return!![];}else{return false;}},g15MiniBleDevices(nBluetoothName){var _0xff7b;let bluetoothName=nBluetoothName;_0xff7b=(484644^484652)+(750229^750224);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3000G".split("").reverse().join(""))){return!![];}else{return false;}},g15ProBleDevices(nBluetoothName){var _0x388ede=(401953^401958)+(671942^671951);let bluetoothName=nBluetoothName;_0x388ede="mejnqk".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A5100G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0036")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0035\u0042")){return!![];}else{return false;}},g18ProBleDevices(nBluetoothName){var _0x3089f=(144575^144569)+(847846^847854);let bluetoothName=nBluetoothName;_0x3089f=(158528^158530)+(949396^949397);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0032\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0032\u0042")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0034")){return!![];}else{return false;}},g19MPlusBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0037\u0041")){return!![];}else{return false;}},g19PlusBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A6200G".split("").reverse().join(""))){return!![];}else{return false;}},g28BleDevices(nBluetoothName){var _0xcd1eb=(542918^542927)+(322317^322312);let bluetoothName=nBluetoothName;_0xcd1eb='\u0067\u006A\u0066\u0070\u0068\u0068';if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3400G".split("").reverse().join(""))){return!![];}else{return false;}},q11PlusBleDevices(nBluetoothName){var _0x_0xaa6;let bluetoothName=nBluetoothName;_0x_0xaa6=(156392^156385)+(357865^357864);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0031\u0031\u0041")){return!![];}else{return false;}},q11ProBleDevices(nBluetoothName){var _0x3f2a=(122414^122408)+(761362^761362);let bluetoothName=nBluetoothName;_0x3f2a=(232345^232336)+(720997^720993);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0031\u0033\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("B3100D".split("").reverse().join(""))){return!![];}else{return false;}},q15BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("8000D".split("").reverse().join(""))){return!![];}else{return false;}},q15MiniBleDevices(nBluetoothName){var _0xfcc59d;let bluetoothName=nBluetoothName;_0xfcc59d="pldmcl".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A4000D".split("").reverse().join(""))){return!![];}else{return false;}},q15ProBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0030\u0039\u0041")){return!![];}else{return false;}},q18BleDevices(nBluetoothName){var _0x3c_0x536=(551374^551367)+(739645^739642);let bluetoothName=nBluetoothName;_0x3c_0x536=997611^997611;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A0100D".split("").reverse().join(""))){return!![];}else{return false;}},q19PlusBleDevices(nBluetoothName){var _0x1cc89a;let bluetoothName=nBluetoothName;_0x1cc89a=324755^324763;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A2100D".split("").reverse().join(""))){return!![];}else{return false;}},g21BleDevices(nBluetoothName){var _0xe7b93c;let bluetoothName=nBluetoothName;_0xe7b93c="knbbhd".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A4200G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0039")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("4100D".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("0400G".split("").reverse().join(""))){return!![];}else{return false;}},gSeriesBleDevices(nBluetoothName){var _0x64efe=(177027^177028)+(216491^216489);let bluetoothName=nBluetoothName;_0x64efe='\u006B\u0068\u0070\u006A\u006D\u0063';if(this['\u0067\u0031\u0035\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0065\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u004D\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u004D\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0069\u006E\u0069\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0038\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0039\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0039\u004D\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0031\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0031\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u004D\u0069\u006E\u0069\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0039\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}},allBleDevices(nBluetoothName){var _0x11fb;let bluetoothName=nBluetoothName;_0x11fb=(109285^109293)+(856258^856256);if(bluetoothName){if(this['\u0074\u0035\u0030\u0050\u0072\u006F\u0041\u006E\u0064\u0054\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}}},g21OrG28BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName){if(this['\u0067\u0032\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}}},setPhoneModel(nPhoneModel){var _0xb51f1f=(756488^756491)+(150627^150628);let phoneModel=nPhoneModel;_0xb51f1f=831521^831526;if(phoneModel){if(phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("e9 CC IM".split("").reverse().join(""))||phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("00MVDP".split("").reverse().join(""))||phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0050\u0042\u0041\u004D\u0030\u0030")){this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065']=!![];}else{this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065']=false;}}},getPhoneModel(){return this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065'];},getFDpiValue(){return this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},setFDpiValue(nValue){const that=this;var _0x71f=(102583^102582)+(587857^587864);let value=nValue;_0x71f="holhpo".split("").reverse().join("");var _0x9e5fda;let bluetoothName=that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'];_0x9e5fda=507303^507299;if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A4000PM".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0035\u0041")){this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=171893^171897;}else if(this['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=value;}else{this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=765482^765474;}}},getMaxDotValueType(nBluetoothName){var _0xd_0xa3a=(934876^934873)+(709885^709881);let bluetoothName=nBluetoothName;_0xd_0xa3a="ppmemg".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0034\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0035\u0041")){return!![];}else{return false;}}};
import bleManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C\u002E\u006A\u0073";import baseSupPrint from"\u002E\u002F\u0042\u0061\u0073\u0065\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u002E\u006A\u0073";import ptFlag from"\u002E\u002F\u0050\u0052\u0049\u004E\u0054\u0045\u0052\u005F\u0046\u004C\u0041\u0047\u002E\u006A\u0073";import bleToothManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065\u002E\u006A\u0073";import constants from"\u002E\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";export default{'\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C':0x10,'\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041':0x11,'\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045':0x12,"CMD_STATR_PRINT":0x13,'\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u0046\u0052\u004D\u005F\u0046\u0049\u0052\u004D\u0057\u0041\u0052\u0045\u005F\u0042\u0055\u004C\u004B':0xc6,'\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054':0x14,"CMD_NEXT_ZIPPEDBULK":0x5c,"CMD_READ_FIRMWARE_REV":0xc5,'\u0043\u004D\u0044\u005F\u0054\u0052\u0041\u004E\u0053\u0046\u0045\u0052':0xf0,'\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0052\u0046\u0049\u0044\u005F\u0044\u0041\u0054\u0041':0x5d,'\u0043\u004D\u0044\u005F\u0052\u0045\u0041\u0044\u005F\u0052\u0045\u0056':0x17,"CMD_READ_DPI":0x22,'\u0045\u0052\u0052\u004F\u0052\u005F\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041':123,"CMD_INQUIRY_STA_DPI":456,'\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068':500,'\u0063\u006E\u0074':0,'\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073':"\u0069\u006E\u0069\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073","frameNumber":0,"currentCommand":0,"imageDataListAll":[],'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074':[],'\u0069\u0073\u0053\u0074\u006F\u0070':false,'\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B':null,'\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061':[],'\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E':'',"paperType":1,'\u0067\u0061\u0070':3,"speed":30,'\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065':false,'\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D':0,'\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D':0,"prtStarNum":0,'\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D':0,'\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065':'','\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B':null,"dipCallBack":null,"readDpiCallBack":null,"printMessage":{'\u006D\u0073\u0067\u0054\u0079\u0070\u0065':"\u0030",'\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D':0},notifyData(notifyMessage){console['\u006C\u006F\u0067']("\u63A5\u6536\u5230\u6570\u636E",JSON['\u0073\u0074\u0072\u0069\u006E\u0067\u0069\u0066\u0079'](notifyMessage));this['\u0068\u0061\u006E\u0064\u006C\u0065\u004E\u006F\u0074\u0069\u0066\u0079'](notifyMessage);},doSupVanPrint(supvanImageData,nObjectData){var _0xd81d=(976817^976816)+(281614^281606);const that=this;_0xd81d="ecagnh".split("").reverse().join("");console['\u006C\u006F\u0067']("51gslitutnirpnavpus".split("").reverse().join(""));let objectData=nObjectData;that['\u0069\u0073\u0053\u0074\u006F\u0070']=false;that['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];that['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']=648739^648739;ptFlag['\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074']=522206^522206;that['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']=689832^689832;that['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']=960099^960075;that['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']=830613^830653;that['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']=250909^250933;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=supvanImageData;console['\u006C\u006F\u0067']("\u6253\u5370\u5B57\u6A21\u003A\u0020",supvanImageData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();that['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0069\u006E\u0069\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073";that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']=objectData['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'];that['\u0070\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']=objectData['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065'];that['\u0067\u0061\u0070']=objectData['\u0047\u0061\u0070'];that['\u0073\u0070\u0065\u0065\u0064']=objectData['\u0053\u0070\u0065\u0065\u0064'];that['\u0073\u0074\u0061\u0072\u0074\u0050\u0072\u0069\u006E\u0074']();},startPrint(){this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'],491599^491599);},queryDPI(callBack){this['\u0064\u0069\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=callBack;this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041\u005F\u0044\u0050\u0049'];console['\u006C\u006F\u0067']("\u6001\u72B6\u8BE2\u67E5".split("").reverse().join(""));baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'],203412^203412);},handleNotify(notifyMessage){switch(this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']){case this['\u0043\u004D\u0044\u005F\u0052\u0045\u0041\u0044\u005F\u0044\u0050\u0049']:let _0x2db;let dpiValue=(notifyMessage[515040^515062]&(322422^322441))+((notifyMessage[644007^644016]&(165542^165465))<<(919267^919275));_0x2db="ihondo".split("").reverse().join("");console['\u006C\u006F\u0067']("\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065",dpiValue);this['\u0072\u0065\u0061\u0064\u0044\u0070\u0069\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](dpiValue);break;case this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041\u005F\u0044\u0050\u0049']:if(baseSupPrint['\u0068\u0061\u006E\u0064\u006C\u0065\u0049\u006E\u0071\u0075\u0069\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073'](notifyMessage)){console['\u006C\u006F\u0067']("\u8FD4\u56DE\u7684\u0064\u0070\u0069",ptFlag['\u0063\u0068\u0061\u006E\u0067\u0065\u0044\u0070\u0069']);this['\u0064\u0069\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](ptFlag['\u0063\u0068\u0061\u006E\u0067\u0065\u0044\u0070\u0069']);}break;case this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045']:this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();break;case this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041']:if(baseSupPrint['\u0068\u0061\u006E\u0064\u006C\u0065\u0049\u006E\u0071\u0075\u0069\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073'](notifyMessage)){this['\u0068\u0061\u006E\u0064\u006C\u0065\u0053\u0074\u0065\u0070']();}break;case this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0053\u0074\u0065\u0070']();break;case this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0053\u0065\u0063\u006F\u006E\u0064'](this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);break;case this['\u0043\u004D\u0044\u005F\u0054\u0052\u0041\u004E\u0053\u0046\u0045\u0052']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0054\u0068\u0069\u0072\u0064']();break;case this['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C']:this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']=441923^441963;this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']=490602^490562;this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']=245659^245683;console['\u006C\u006F\u0067']("*****************************\u590D\u56DE\u4EE4\u547D\u6210\u5B8C\u6EE1\u5FD7\u5230\u6536\u63A5******************".split("").reverse().join(""));if(this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']>(866301^866301)){console['\u006C\u006F\u0067']("\u4E0B\u53D1\u4E0B\u4E00\u4E2A\u7F13\u51B2\u533A");this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},413776^413848);}else{this['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']=this['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']+(966048^966049);if(this['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']>=bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()){console['\u006C\u006F\u0067']("\u6253\u5370\u5B8C\u6210");this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}else{if(this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']==constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0049\u004D\u0041\u0047\u0045']){bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0049\u006D\u0061\u0067\u0065']();}else if(this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']==constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058']){bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u004D\u0061\u0074\u0072\u0069\u0078']();}}}break;case this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054']:this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=519022^519022;if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();break;}},handleStep(){switch(this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']){case"sutatSyreuQtini".split("").reverse().join(""):if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(245203^245202)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();}if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}return;}this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="eciveDkcehc".split("").reverse().join("");this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045'],815732^815732);break;case"\u0073\u0074\u0061\u0072\u0074\u0050\u0072\u0069\u006E\u0074":if(this['\u0069\u0073\u0053\u0074\u006F\u0070']){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']("\u7EC8\u6B62\u6253\u5370\u6210\u529F"));this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();}else{setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},618691^618507);}break;case"\u0073\u0065\u006E\u0064\u004D\u0061\u0074\u0072\u0069\u0078\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073":if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){return;}if(ptFlag['\u0062\u0075\u0066\u0053\u0074\u0061']==(347909^347909)){if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(843586^843587)){this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070'](this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);}else{this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']--;if(this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']<=(116790^116790)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},560437^560391);}}else{this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']--;if(this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']<=(586186^586186)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},132501^132519);}break;case"sutatSyreuQtnirPpots".split("").reverse().join(""):this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054'],814893^814893);break;case"eciveDkcehc".split("").reverse().join(""):this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']--;if(this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']<=(405672^405672)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){return;}if(ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u006D\u0045\u0078\u0065\u0053\u0074\u0061']==(831799^831799)){this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0073\u0074\u0061\u0072\u0074\u0050\u0072\u0069\u006E\u0074";this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054'],667449^667449);}else{setTimeout(()=>{this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},993340^993294);}break;case"\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068":console['\u006C\u006F\u0067']("\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E",ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']);this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']--;if(this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']<=(726384^726384)){return;}if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(584452^584453)){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}else{this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']=ptFlag['\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074'];if(this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']==(104293^104293)){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006E\u0075\u006D':this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']}));}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']));}}break;}},handleTransferNext(){if(this['\u0069\u0073\u0053\u0074\u006F\u0070']){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']("\u7EC8\u6B62\u6253\u5370\u6210\u529F"));this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="sutatSyreuQxirtaMdnes".split("").reverse().join("");this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},handleTransferStep(btData){if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](!![],!![])){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}return;}this['\u0063\u006E\u0074']=parseInt((btData['\u006C\u0065\u006E\u0067\u0074\u0068']+this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']-(588946^588947))/this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']);this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050\u0053\u0074\u0061\u0072\u0074\u0054\u0072\u0061\u006E\u0073'](this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B'],790606^791118,this['\u0063\u006E\u0074']);},handleTransferStepThird(){const that=this;that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']++;if(that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']<that['\u0063\u006E\u0074']){that['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0053\u0065\u0063\u006F\u006E\u0064'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);}else{setTimeout(()=>{that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']=582406^582406;that['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=that['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](that['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C'],200315^200315);},622487^622493);}},handleTransferStepSecond(btData){let btWrite=new Uint8Array(this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']+(750317^750315));btWrite[810947^810947]=428092^428182;btWrite[571664^571665]=209972^210063;btWrite[582045^582047]=625114^625114;btWrite[333618^333617]=488764^488764;btWrite[213215^213211]=this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072'];btWrite[738014^738011]=this['\u0063\u006E\u0074'];if((this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+(709760^709761))*this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']<btData['\u006C\u0065\u006E\u0067\u0074\u0068']){for(var k=141512^141512;k<this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068'];k++){btWrite[(462461^462459)+k]=btData[this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']*this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+k];}}else{let _0x6f5ea;let nc=btData['\u006C\u0065\u006E\u0067\u0074\u0068']-this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']*this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068'];_0x6f5ea="pheeli".split("").reverse().join("");for(var k=854648^854648;k<nc;k++){btWrite[(361187^361189)+k]=btData[this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']*this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+k];}}let _0xfc0a5c;let chcksum=300341^300341;_0xfc0a5c=(993670^993664)+(916928^916932);for(var i=224025^224029;i<btWrite['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){chcksum+=btWrite[i]&(460192^460127);}btWrite[956088^956090]=chcksum;btWrite[812595^812592]=chcksum>>(368911^368903);this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0054\u0052\u0041\u004E\u0053\u0046\u0045\u0052'];this['\u0074\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0044\u0061\u0074\u0061\u004C\u0050'](btWrite);},transferDataLP(btdata){let _0x88b;let message=new Uint8Array(231865^232377);_0x88b=(763724^763721)+(753418^753422);message[760754^760754]=928895^928769;message[861454^861455]=721801^721875;message[451831^451829]=393492^393704;message[428998^428997]=157196^157197;message[471749^471745]=857138^857122;message[618167^618162]=968769^968771;for(var i=463476^463474;i<btdata['\u006C\u0065\u006E\u0067\u0074\u0068']+(593432^593438);i++){message[i]=btdata[i-(520126^520120)];}this['\u0073\u0065\u006E\u0064\u0044\u0061\u0074\u0061\u0047\u0031\u0035'](message);},sendDataG15(message){let byteArrayList=[];let i=167945^167945;let count=475105^475105;while(i<(119112^119116)){var _0xbf697c=(759950^759948)+(754528^754531);let listData=message['\u0073\u0075\u0062\u0061\u0072\u0072\u0061\u0079'](i*(242782^242910),(839600^839472)+i*(962571^962699));_0xbf697c='\u006E\u0066\u006A\u006B\u0067\u006A';byteArrayList['\u0070\u0075\u0073\u0068'](listData);i++;}let _0xc8a;var timer=setInterval(()=>{if(count<(498036^498032)){bleManage['\u006F\u006E\u0057\u0072\u0069\u0074\u0065\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065'](byteArrayList[count]);count++;}else{clearInterval(timer);}},968275^968303);_0xc8a="ldnqjn".split("").reverse().join("");},stopPrint(){this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073";console['\u006C\u006F\u0067']("\u67E5\u8BE2\u72B6\u6001");this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();return false;},devCheckErrMsg(isBusy,isPrintFinish){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=false;if(ptFlag['\u0046\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E']!=(652980^652980)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0036'];console['\u006C\u006F\u0067']("\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E",ptFlag['\u0046\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064']!=(578236^578236)){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0037'];console['\u006C\u006F\u0067']("\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064",ptFlag['\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072']!=(220155^220155)){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0039'];console['\u006C\u006F\u0067']("rorrEetirWdaeRlebal".split("").reverse().join(""),ptFlag['\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072']!=(628417^628417)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0030'];console['\u006C\u006F\u0067']("\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072",ptFlag['\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065']!=(129184^129184)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0031'];console['\u006C\u006F\u0067']("\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065\u72B6\u0031\u6001\u503C",ptFlag['\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072']!=(323696^323696)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0033'];console['\u006C\u006F\u0067']("\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072\u72B6\u0031\u6001\u503C",ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}if(isBusy){if(ptFlag['\u0064\u0065\u0076\u0069\u0063\u0065\u0042\u0075\u0073\u0079']==(843760^843760)&&ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(575337^575337)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0032'];console['\u006C\u006F\u0067']("\u0064\u0065\u0076\u0069\u0063\u0065\u0042\u0075\u0073\u0079",ptFlag['\u0064\u0065\u0076\u0069\u0063\u0065\u0042\u0075\u0073\u0079']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}}return false;},doPrintNextPage(nImageDataListNext){var _0x53d49g=(954496^954496)+(857291^857292);let imageDataListNext=nImageDataListNext;_0x53d49g=587716^587718;this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=imageDataListNext;console['\u006C\u006F\u0067']("\u7B2C\u4E8C\u4EFD\u6253\u5370\u5B57\u6A21\u957F\u5EA6\u003A\u0020",this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']);this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;},printFinish(){setTimeout(()=>{this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="hsiniFtnirp".split("").reverse().join("");this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},478461^478441);},stopPrintSupvan(){this['\u0069\u0073\u0053\u0074\u006F\u0070']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];},queryStatus(){this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'],177166^177166);},stopPrintCallback(stopCallBack){this['\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=stopCallBack;}};
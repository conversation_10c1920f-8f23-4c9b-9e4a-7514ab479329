import bleManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C\u002E\u006A\u0073";import baseSupPrint from"\u002E\u002F\u0042\u0061\u0073\u0065\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u002E\u006A\u0073";import ptFlag from"\u002E\u002F\u0050\u0052\u0049\u004E\u0054\u0045\u0052\u005F\u0046\u004C\u0041\u0047\u002E\u006A\u0073";import bleToothManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065\u002E\u006A\u0073";import constants from"\u002E\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";export default{'\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C':0x10,'\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041':0x11,"CMD_CHECK_DEVICE":0x12,'\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054':0x13,'\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u0046\u0052\u004D\u005F\u0046\u0049\u0052\u004D\u0057\u0041\u0052\u0045\u005F\u0042\u0055\u004C\u004B':0xc6,"CMD_STOP_PRINT":0x14,"CMD_NEXT_ZIPPEDBULK":0x5c,"CMD_READ_FIRMWARE_REV":0xc5,"CMD_TRANSFER":0xf0,'\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0052\u0046\u0049\u0044\u005F\u0044\u0041\u0054\u0041':0x5d,'\u0043\u004D\u0044\u005F\u0052\u0045\u0041\u0044\u005F\u0052\u0045\u0056':0x17,"CMD_SET_PRTMODE":0x33,'\u0043\u004D\u0044\u005F\u0053\u0045\u004E\u0044\u005F\u0049\u004E\u0046':0x35,'\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068':500,"cnt":0,'\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073':'initQueryStatus','\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072':0,"currentCommand":0,"imageDataListAll":[],'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074':[],'\u0069\u0073\u0053\u0074\u006F\u0070':false,'\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B':null,'\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061':[],"deviceSn":'','\u0070\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065':1,'\u0064\u0065\u0065\u0070\u006E\u0065\u0073\u0073':4,'\u0067\u0061\u0070':3,"speed":30,"noInstallType":false,'\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D':0,'\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D':0,"prtStarNum":0,'\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D':0,"printType":'','\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B':null,'\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061':{},"printMessage":{'\u006D\u0073\u0067\u0054\u0079\u0070\u0065':"\u0030",'\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D':0},notifyData(notifyMessage){console['\u006C\u006F\u0067']("\u63A5\u6536\u5230\u6570\u636E",JSON['\u0073\u0074\u0072\u0069\u006E\u0067\u0069\u0066\u0079'](notifyMessage));this['\u0068\u0061\u006E\u0064\u006C\u0065\u004E\u006F\u0074\u0069\u0066\u0079'](notifyMessage);},doSupVanPrint(supvanImageData,nObjectData){let _0x1f4bag;const that=this;_0x1f4bag=(230115^230114)+(317190^317188);console['\u006C\u006F\u0067']("\u0073\u0075\u0070\u0076\u0061\u006E\u0070\u0072\u0069\u006E\u0074\u0075\u0074\u0069\u006C\u0073\u006D\u0070\u0035\u0030");let _0x8f81a;let objectData=nObjectData;_0x8f81a="qdmloe".split("").reverse().join("");that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']=nObjectData;that['\u0069\u0073\u0053\u0074\u006F\u0070']=false;that['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];that['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']=759852^759852;ptFlag['\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074']=139363^139363;that['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']=309731^309731;that['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']=244080^244056;that['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']=248053^248029;that['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']=530839^530879;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=supvanImageData;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();that['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0069\u006E\u0069\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073";that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']=objectData['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'];that['\u0070\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']=objectData['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065'];that['\u0067\u0061\u0070']=objectData['\u0047\u0061\u0070'];that['\u0073\u0070\u0065\u0065\u0064']=objectData['\u0053\u0070\u0065\u0065\u0064'];that['\u0064\u0065\u0065\u0070\u006E\u0065\u0073\u0073']=objectData['\u0044\u0065\u006E\u0073\u0069\u0074\u0079'];that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061']=[405803^405787,625775^625775,189926^189926,111364^111364,217164^217164,977744^977744,609575^609575,259073^259073,725665^725665,225442^225442,424183^424183,146006^146006,818749^818749,293708^293708,631033^631033,452700^452700,457827^457754,705551^705550,978305^978304,873139^873192,581055^580948,145107^145038,774055^773948,827627^827480,820848^820800,430243^430294,375511^375510,483261^483215,126455^126405,857063^857062,948620^948623,185849^185849,890105^889881,316765^316764,455072^455072,190996^190996,374008^373852,548097^548103,326494^326638,235621^235617,472333^472346,690450^690458,335195^335178,450258^450265,169317^169301,908830^908839,146239^146239,378155^378155,764147^764020,731829^731753,887677^887786,986982^987051,516279^516278,792739^792643,411520^411423,169946^169882,546487^546338,359873^359813,661204^661145,149335^149458,168060^168080,480097^480198,634209^634284,617533^617533,612916^612916,140115^140115,270719^270719,822328^822328,990835^990835,730217^730219,555448^555448,162195^162195,899017^899017,796702^796702,829821^829821,120285^120285,347315^347315,506701^506701,920510^920510,547503^547503];that['\u0073\u0065\u0074\u0050\u0072\u0074\u004D\u006F\u0064\u0065']();},setPrtMode(){this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0050\u0052\u0054\u004D\u004F\u0044\u0045'];console['\u006C\u006F\u0067']("\u0073\u0065\u0074\u0050\u0072\u0074\u004D\u006F\u0064\u0065");baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0050\u0052\u0054\u004D\u004F\u0044\u0045'],792183^792183);},sendINF(){console['\u006C\u006F\u0067']("\u5EA6\u6D53\u7F6E\u8BBE".split("").reverse().join(""));this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0053\u0045\u004E\u0044\u005F\u0049\u004E\u0046'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0053\u0045\u004E\u0044\u005F\u0049\u004E\u0046'],this['\u0064\u0065\u0065\u0070\u006E\u0065\u0073\u0073']);},startPrint(objectData){var _0xf95fd=(511657^511661)+(620603^620601);const that=this;_0xf95fd=648375^648383;if(that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061']['\u006C\u0065\u006E\u0067\u0074\u0068']==(777706^777706)){this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();}else{that['\u0073\u0065\u006E\u0064\u0052\u0066\u0069\u0064\u0044\u0061\u0074\u0061'](objectData);}},sendRfidData(nObjectData){const that=this;var _0xbba=(479606^479602)+(291410^291409);let objectData=nObjectData;_0xbba=253608^253610;console['\u006C\u006F\u0067']("\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061",JSON['\u0073\u0074\u0072\u0069\u006E\u0067\u0069\u0066\u0079'](objectData));that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][428763^428737]=that['\u0070\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065'];that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][134138^134116]=that['\u0067\u0061\u0070'];that['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=that['\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0052\u0046\u0049\u0044\u005F\u0044\u0041\u0054\u0041'];if(bleManage['\u0067\u0065\u0074\u0050\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C']()){baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](that['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'],925495^925495);}else{if(that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][999786^999792]==(872716^872718)){that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][937787^937761]=344311^344306;}if(objectData['\u0052\u006F\u0074\u0061\u0074\u0065']==(959307^959307)||objectData['\u0052\u006F\u0074\u0061\u0074\u0065']==(132433^132432)){that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][618711^618700]=objectData['\u0057\u0069\u0064\u0074\u0068'];that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][384211^384207]=objectData['\u0048\u0065\u0069\u0067\u0068\u0074'];}else{that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][811129^811106]=objectData['\u0048\u0065\u0069\u0067\u0068\u0074'];that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][761929^761941]=objectData['\u0057\u0069\u0064\u0074\u0068'];}that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][931938^931965]=309404^309404;console['\u006C\u006F\u0067']("difr\u53D1\u4E0B".split("").reverse().join(""),that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061']);baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u0052\u0061\u006E\u0064\u006F\u006D'](that['\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0052\u0046\u0049\u0044\u005F\u0044\u0041\u0054\u0041'],that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061']);}},handleNotify(notifyMessage){switch(this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']){case this['\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0050\u0052\u0054\u004D\u004F\u0044\u0045']:this['\u0073\u0065\u006E\u0064\u0049\u004E\u0046']();break;case this['\u0043\u004D\u0044\u005F\u0053\u0045\u004E\u0044\u005F\u0049\u004E\u0046']:this['\u0073\u0074\u0061\u0072\u0074\u0050\u0072\u0069\u006E\u0074'](this['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']);break;case this['\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0052\u0046\u0049\u0044\u005F\u0044\u0041\u0054\u0041']:this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();break;case this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045']:this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();break;case this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041']:if(baseSupPrint['\u0068\u0061\u006E\u0064\u006C\u0065\u0049\u006E\u0071\u0075\u0069\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073'](notifyMessage)){this['\u0068\u0061\u006E\u0064\u006C\u0065\u0053\u0074\u0065\u0070']();}break;case this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0053\u0074\u0065\u0070']();break;case this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0053\u0065\u0063\u006F\u006E\u0064'](this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);break;case this['\u0043\u004D\u0044\u005F\u0054\u0052\u0041\u004E\u0053\u0046\u0045\u0052']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0054\u0068\u0069\u0072\u0064']();break;case this['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C']:this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']=530010^530034;this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']=778059^778083;this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']=485438^485398;this['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']++;console['\u006C\u006F\u0067']("\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u63A5\u6536\u5230\u5FD7\u6EE1\u5B8C\u6210\u547D\u4EE4\u56DE\u590D\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A");if(this['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']>=bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}else{if(this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']==constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0049\u004D\u0041\u0047\u0045']){bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0049\u006D\u0061\u0067\u0065']();}else if(this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']==constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058']){bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u004D\u0061\u0074\u0072\u0069\u0078']();}}break;case this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054']:this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=656057^656057;if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();break;}},handleStep(){switch(this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']){case"sutatSyreuQtini".split("").reverse().join(""):if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(906513^906512)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();}if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}return;}this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0063\u0068\u0065\u0063\u006B\u0044\u0065\u0076\u0069\u0063\u0065";this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045'],819315^819315);break;case"\u0073\u0074\u0061\u0072\u0074\u0050\u0072\u0069\u006E\u0074":if(this['\u0069\u0073\u0053\u0074\u006F\u0070']){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']("\u529F\u6210\u5370\u6253\u6B62\u7EC8".split("").reverse().join("")));this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();}else{setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},738334^738518);}break;case"\u0073\u0065\u006E\u0064\u004D\u0061\u0074\u0072\u0069\u0078\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073":if(ptFlag['\u0062\u0075\u0066\u0053\u0074\u0061']==(133657^133657)){if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){return;}if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(275849^275848)){this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070'](this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);}else{this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']--;if(this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']<=(837229^837229)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},691476^691494);}}else{this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']--;if(this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']<=(877266^877266)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},133633^133683);}break;case"\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073":this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054'],442220^442220);break;case"eciveDkcehc".split("").reverse().join(""):this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']--;if(this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']<=(813293^813293)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){return;}if(ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u006D\u0045\u0078\u0065\u0053\u0074\u0061']==(916757^916757)){this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0073\u0074\u0061\u0072\u0074\u0050\u0072\u0069\u006E\u0074";this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054'],630073^630073);}else{setTimeout(()=>{this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},606918^606964);}break;case"hsiniFtnirp".split("").reverse().join(""):console['\u006C\u006F\u0067']("\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E",ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']);if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(715041^715040)){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}else{this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']=ptFlag['\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074'];if(this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']==(722372^722372)){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({"num":this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']}));}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']));}}break;}},handleTransferNext(){if(this['\u0069\u0073\u0053\u0074\u006F\u0070']){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']("\u7EC8\u6B62\u6253\u5370\u6210\u529F"));this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0073\u0065\u006E\u0064\u004D\u0061\u0074\u0072\u0069\u0078\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073";this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},handleTransferStep(btData){if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](!![],!![])){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}return;}this['\u0063\u006E\u0074']=parseInt((btData['\u006C\u0065\u006E\u0067\u0074\u0068']+this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']-(388836^388837))/this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']);this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050\u0053\u0074\u0061\u0072\u0074\u0054\u0072\u0061\u006E\u0073'](this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B'],909590^910102,this['\u0063\u006E\u0074']);},handleTransferStepThird(){let _0xa_0xa27;const that=this;_0xa_0xa27=427519^427516;that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']++;if(that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']<that['\u0063\u006E\u0074']){that['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0053\u0065\u0063\u006F\u006E\u0064'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);}else{setTimeout(()=>{that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']=681029^681029;that['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=that['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050\u0053\u0074\u0061\u0072\u0074\u0054\u0072\u0061\u006E\u0073'](that['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C'],that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']['\u006C\u0065\u006E\u0067\u0074\u0068'],that['\u0073\u0070\u0065\u0065\u0064']);},649363^649377);}},handleTransferStepSecond(btData){let btWrite=new Uint8Array(this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']+(566991^566985));btWrite[950482^950482]=182944^182794;btWrite[220580^220581]=720132^720319;btWrite[469666^469664]=281575^281575;btWrite[409299^409296]=360591^360591;btWrite[567272^567276]=this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072'];btWrite[271952^271957]=this['\u0063\u006E\u0074'];if((this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+(487979^487978))*this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']<btData['\u006C\u0065\u006E\u0067\u0074\u0068']){for(var k=729468^729468;k<this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068'];k++){btWrite[(962037^962035)+k]=btData[this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']*this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+k];}}else{let nc=btData['\u006C\u0065\u006E\u0067\u0074\u0068']-this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']*this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068'];for(var k=725863^725863;k<nc;k++){btWrite[(540289^540295)+k]=btData[this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']*this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+k];}}let _0xc0e3ga;let chcksum=567581^567581;_0xc0e3ga='\u006A\u006F\u0063\u0068\u006F\u0069';for(var i=260773^260769;i<btWrite['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){chcksum+=btWrite[i]&(576657^576622);}btWrite[319546^319544]=chcksum;btWrite[537856^537859]=chcksum>>(243203^243211);this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0054\u0052\u0041\u004E\u0053\u0046\u0045\u0052'];this['\u0074\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0044\u0061\u0074\u0061\u004C\u0050'](btWrite);},transferDataLP(btdata){var _0x932b=(365420^365421)+(873320^873322);let message=new Uint8Array(428122^428634);_0x932b=(629861^629869)+(709999^709995);message[440711^440711]=633600^633726;message[815523^815522]=409477^409567;message[211664^211666]=868918^869066;message[331013^331014]=662104^662105;message[657139^657143]=216491^216507;message[841415^841410]=286427^286425;for(var i=395072^395078;i<btdata['\u006C\u0065\u006E\u0067\u0074\u0068']+(839217^839223);i++){message[i]=btdata[i-(567590^567584)];}this['\u0073\u0065\u006E\u0064\u0044\u0061\u0074\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](message);},sendDataT50Pro(message){let _0x78g9d;const that=this;_0x78g9d='\u0066\u006E\u0071\u006B\u0069\u0065';let byteArrayList=[];var _0x_0xcbd=(459320^459313)+(392548^392546);let i=804276^804276;_0x_0xcbd='\u0068\u006B\u0070\u006D\u006E\u0070';var _0x6bf42c=(166078^166073)+(285603^285603);let count=652387^652387;_0x6bf42c=(927017^927020)+(879932^879933);while(i<(329382^329378)){let _0xb_0x0c2;let listData=message['\u0073\u0075\u0062\u0061\u0072\u0072\u0061\u0079'](i*(723758^723886),(961818^961946)+i*(659596^659468));_0xb_0x0c2=(196263^196259)+(542644^542644);byteArrayList['\u0070\u0075\u0073\u0068'](listData);i++;}var _0x42a9ef=(576747^576747)+(444539^444543);var timer=setInterval(()=>{if(count==(271623^271619)){clearInterval(timer);setTimeout(()=>{that['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0054\u0068\u0069\u0072\u0064']();},488178^488128);return;}bleManage['\u006F\u006E\u0057\u0072\u0069\u0074\u0065\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065'](byteArrayList[count]);count++;},778894^778940);_0x42a9ef=(371186^371194)+(503759^503756);},stopPrint(){this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073";console['\u006C\u006F\u0067']("\u67E5\u8BE2\u72B6\u6001");this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();return false;},devCheckErrMsg(isBusy,isPrintFinish){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=false;if(ptFlag['\u0046\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E']!=(756567^756567)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0036'];console['\u006C\u006F\u0067']("\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E",ptFlag['\u0046\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064']!=(899801^899801)){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0037'];console['\u006C\u006F\u0067']("\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064",ptFlag['\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0045\u006E\u0064']!=(803710^803710)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0038'];console['\u006C\u006F\u0067']("\u0052\u0069\u0062\u0045\u006E\u0064",ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0045\u006E\u0064']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072']!=(304781^304781)){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0039'];console['\u006C\u006F\u0067']("\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072",ptFlag['\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072']!=(981922^981922)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0030'];console['\u006C\u006F\u0067']("rorrEedoMlebal".split("").reverse().join(""),ptFlag['\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065']!=(708002^708002)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0031'];console['\u006C\u006F\u0067']("\u503C\u60011\u72B6eroMoNlebal".split("").reverse().join(""),ptFlag['\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072']!=(210649^210649)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0033'];console['\u006C\u006F\u0067']("\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072\u72B6\u0031\u6001\u503C",ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}if(isBusy){if(ptFlag['\u0064\u0065\u0076\u0069\u0063\u0065\u0042\u0075\u0073\u0079']==(721346^721346)&&ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(499577^499577)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0032'];console['\u006C\u006F\u0067']("ysuBecived".split("").reverse().join(""),ptFlag['\u0064\u0065\u0076\u0069\u0063\u0065\u0042\u0075\u0073\u0079']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}}return false;},doPrintNextPage(nImageDataListNext){let imageDataListNext=nImageDataListNext;this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=imageDataListNext;console['\u006C\u006F\u0067']("\u7B2C\u4E8C\u4EFD\u6253\u5370\u5B57\u6A21\u957F\u5EA6\u003A\u0020",this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']['\u006C\u0065\u006E\u0067\u0074\u0068']);this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;},printFinish(){setTimeout(()=>{this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068";this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},113995^114015);},stopPrintSupvan(){this['\u0069\u0073\u0053\u0074\u006F\u0070']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];},queryStatus(){this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'],887686^887686);},stopPrintCallback(stopCallBack){this['\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=stopCallBack;}};
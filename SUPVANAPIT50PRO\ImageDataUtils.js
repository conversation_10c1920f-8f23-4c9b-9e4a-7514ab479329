import bleManage from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C";export default{"bt":[],"width":0,'\u0068\u0065\u0069\u0067\u0068\u0074':0,'\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070':0,'\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D':0,"printerSn":0,"dpiValue":8,getAllBytes(objectData){console['\u006C\u006F\u0067']("\u0031\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061",objectData);var _0x6a2dcc=(408045^408042)+(319133^319133);const that=this;_0x6a2dcc=295927^295926;let imageRgbaData=objectData['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061'];let imageWidth=objectData['\u0057\u0069\u0064\u0074\u0068'];var _0xd139d=(320421^320419)+(149604^149602);let imageHeight=objectData['\u0048\u0065\u0069\u0067\u0068\u0074'];_0xd139d="hkpgkg".split("").reverse().join("");let rotate=objectData['\u0072\u006F\u0074\u0061\u0074\u0065\u0041\u006E\u0067\u006C\u0065'];var _0x8c9ff=(334472^334473)+(281775^281775);let offsetX=objectData['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D'];_0x8c9ff=(956232^956234)+(904283^904282);let offsetY=objectData['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D'];let flipType=objectData['\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065'];let _0x3e2fge;let printerSn=objectData['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'];_0x3e2fge=957322^957315;that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleManage['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();console['\u006C\u006F\u0067']("\u0034\u0074\u0068\u0061\u0074\u002E\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065",that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);if(!imageWidth){imageWidth=334601^334600;}if(!imageHeight){imageHeight=438473^438472;}if(!rotate){rotate=603208^603209;}if(!offsetX){offsetX=622541^622541;}if(!offsetY){offsetY=410290^410290;}if(!flipType){flipType=false;}if(!printerSn){printerSn='';}var _0x38b5e=(563499^563496)+(581639^581635);let arr=new Array(imageHeight);_0x38b5e=(641649^641657)+(253507^253508);for(let i=944463^944463;i<imageHeight;i++){let rowArr=new Array(imageWidth);for(let j=478210^478210;j<imageWidth;j++){let index=(i*imageWidth+j)*(141723^141727);let r=imageRgbaData[index];let g=imageRgbaData[index+(710733^710732)];var _0xf87ba=(456452^456449)+(844777^844768);let b=imageRgbaData[index+(799493^799495)];_0xf87ba=(623562^623562)+(669000^669005);var _0xbg0d1c=(306688^306691)+(638509^638508);let a=imageRgbaData[index+(880003^880000)];_0xbg0d1c=(706916^706912)+(417058^417060);let _0x35fb;let value=this['\u0063\u006C\u0061\u006D\u0070'](r,g,b,a);_0x35fb=(138364^138357)+(131463^131458);rowArr[j]=value;}arr[i]=rowArr;}if(rotate!=(190327^190327)){arr=this['\u0072\u006F\u0074\u0061\u0074\u0065\u0044\u0061\u0074\u0061'](arr,imageWidth,imageHeight,rotate);if(rotate==(147066^146976)||rotate==(940621^940867)||rotate==-(840448^840538)){console['\u006C\u006F\u0067']("\u8F6C\u65CB".split("").reverse().join(""),rotate);var _0x4123ba=(968225^968232)+(521822^521819);let temp=imageWidth;_0x4123ba=(677428^677430)+(597691^597691);imageWidth=imageHeight;imageHeight=temp;}}if(flipType>(428591^428591)){arr=this['\u0066\u006C\u0069\u0070\u0044\u0061\u0074\u0061'](arr,492728^492729);arr=this['\u0066\u006C\u0069\u0070\u0044\u0061\u0074\u0061'](arr,373651^373649);}if(offsetX!=(962935^962935)||offsetY!=(700116^700116)){arr=this['\u006F\u0066\u0066\u0073\u0065\u0074\u0044\u0061\u0074\u0061'](arr,imageWidth,imageHeight,offsetX,offsetY);}let _0xg368g;let maxDotValue=765576^765704;_0xg368g='\u0061\u0065\u006E\u0067\u0062\u0066';console['\u006C\u006F\u0067']("\u0070\u0072\u0069\u006E\u0074\u0065\u0072\u0053\u006E",printerSn);if(bleManage['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](printerSn)){maxDotValue=503013^503461;}else if(bleManage['\u0067\u0065\u0074\u004D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065\u0054\u0079\u0070\u0065'](printerSn)){maxDotValue=834289^833713;}else{}if(imageHeight>maxDotValue){imageHeight=maxDotValue;arr=this['\u0073\u0061\u0066\u0065\u0044\u0061\u0074\u0061'](arr,maxDotValue);}that['\u0077\u0069\u0064\u0074\u0068']=imageWidth;that['\u0068\u0065\u0069\u0067\u0068\u0074']=imageHeight;let pData=this['\u0070\u0072\u0069\u006E\u0074\u0044\u0061\u0074\u0061'](arr,imageWidth,imageHeight);return pData;},rotateData(arr2,imageWidth,imageHeight,rotate){if(rotate==(935320^935362)){var _0x4a69b=(120753^120752)+(596379^596382);let bt=new Array(imageWidth);_0x4a69b=(710458^710457)+(444678^444675);for(let i=283067^283067;i<imageWidth;i++){var _0xf652a=(842167^842162)+(246339^246343);let row=new Array(imageHeight);_0xf652a=403242^403246;for(let j=529982^529982;j<imageHeight;j++){row[j]=arr2[imageHeight-j-(918932^918933)][i];}bt[i]=row;}return bt;}else if(rotate==(304645^304817)){let bt=new Array(imageHeight);for(let i=721487^721487;i<imageHeight;i++){var _0x9bb1ed=(925881^925885)+(810914^810923);let row=new Array(imageWidth);_0x9bb1ed=(967447^967443)+(455342^455335);for(let j=502837^502837;j<imageWidth;j++){row[j]=arr2[imageHeight-i-(193553^193552)][imageWidth-j-(596071^596070)];}bt[i]=row;}return bt;}else if(rotate==(792135^792393)||rotate==-(285057^285147)){let bt=new Array(imageWidth);for(let i=896363^896363;i<imageWidth;i++){let _0x_0xc75;let row=new Array(imageHeight);_0x_0xc75=(526369^526376)+(620180^620188);for(let j=833928^833928;j<imageHeight;j++){row[j]=arr2[j][imageWidth-i-(567411^567410)];}bt[i]=row;}return bt;}else{return arr2;}},flipData(arr2,flipTYPE){let imageHeight=arr2['\u006C\u0065\u006E\u0067\u0074\u0068'];let _0xafc7cc;let imageWidth=arr2[960965^960965]['\u006C\u0065\u006E\u0067\u0074\u0068'];_0xafc7cc='\u006B\u006A\u0062\u0062\u0068\u0061';if(flipTYPE==(379845^379844)){let bt=new Array(imageHeight);for(let i=768129^768129;i<imageHeight;i++){var _0x8428f=(580894^580886)+(762045^762042);let row=new Array(imageWidth);_0x8428f=878837^878835;for(let j=865890^865890;j<imageWidth;j++){row[j]=arr2[i][imageWidth-j-(493320^493321)];}bt[i]=row;}return bt;}else if(flipTYPE==(301225^301227)){let _0xd2b7e;let bt=new Array(imageHeight);_0xd2b7e=589697^589698;for(let i=711955^711955;i<imageHeight;i++){let _0x3ae9c;let row=new Array(imageWidth);_0x3ae9c="mkphcl".split("").reverse().join("");for(let j=909514^909514;j<imageWidth;j++){row[j]=arr2[imageHeight-i-(381942^381943)][j];}bt[i]=row;}return bt;}else{return arr2;}},safeData(arr2,maxDotValue){let _0xea_0x50e;let imageHeight=arr2['\u006C\u0065\u006E\u0067\u0074\u0068'];_0xea_0x50e=(154417^154425)+(588998^589007);if(imageHeight<=maxDotValue){return arr2;}let _0xe612ae;let bt=new Array(maxDotValue);_0xe612ae=(757334^757328)+(948996^948998);var _0xb57f=(402178^402187)+(592988^592987);let offsetY=Math['\u0066\u006C\u006F\u006F\u0072']((imageHeight-maxDotValue)/(122590^122588));_0xb57f=463665^463664;for(let i=268019^268019;i<maxDotValue;i++){bt[i]=arr2[i+offsetY];}return bt;},clamp(r,g,b,a){var _0x505ddd=(245306^245309)+(548930^548939);let threshold=307525^307689;_0x505ddd=635869^635864;if(a>(161259^161131)){let _0x3bg96d;let gray=r*0.3+g*0.59+b*0.11;_0x3bg96d='\u006F\u006F\u006F\u0064\u006C\u0070';if(gray<threshold){return 706111^706110;}else{return 475410^475410;}}else{return 420326^420326;}},offsetData(arr2,imageWidth,imageHeight,offsetX,offsetY){let _0x164dbe;let bt=new Array(imageHeight);_0x164dbe=(593044^593047)+(577337^577341);var _0x2_0xd17=(923904^923909)+(223670^223667);let lastHeightIndex=imageHeight-(105180^105181);_0x2_0xd17=(969181^969176)+(245455^245454);var _0x613g=(523966^523964)+(936806^936814);let lastWidthIndex=imageWidth-(900476^900477);_0x613g=789987^789985;for(let i=837389^837389;i<imageHeight;i++){let offsetI=i+offsetX;if(offsetI<(949419^949419)||offsetI>lastHeightIndex){let row=new Array(imageWidth)['\u0066\u0069\u006C\u006C'](798449^798449);bt[i]=row;}else{let row=new Array(imageWidth);for(let j=814779^814779;j<imageWidth;j++){let offsetJ=j-offsetY;if(offsetJ<(145842^145842)||offsetJ>lastWidthIndex){row[j]=333918^333918;}else{row[j]=arr2[offsetI][offsetJ];}}bt[i]=row;}}return bt;},printData(arr2){const that=this;let _0x2e9b;let imageHeight=arr2['\u006C\u0065\u006E\u0067\u0074\u0068'];_0x2e9b=(626336^626336)+(541697^541698);let imageWidth=arr2[771011^771011]['\u006C\u0065\u006E\u0067\u0074\u0068'];console['\u006C\u006F\u0067']("thgieHegami".split("").reverse().join(""),imageHeight);console['\u006C\u006F\u0067']("htdiWegami".split("").reverse().join(""),imageWidth);let _0xd_0x4b9;let needUpdateTop=356556^356557;_0xd_0x4b9=(200741^200749)+(332986^332990);let cx=imageHeight/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];console['\u006C\u006F\u0067']("xc".split("").reverse().join(""),cx);that['\u0062\u0074']=new Array(cx*imageWidth)['\u0066\u0069\u006C\u006C'](910575^910575);for(let i=972776^972776;i<imageWidth;i++){for(let j=595978^595978;j<imageHeight;j++){var _0x62gca=(548222^548216)+(843298^843301);let value=arr2[j][i];_0x62gca=171737^171742;if(value==(126335^126334)){if(needUpdateTop==(804321^804320)){that['\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']=i;needUpdateTop=838967^838967;}that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=i;}let _0x39c71c;let index=cx*i+Math['\u0066\u006C\u006F\u006F\u0072'](j/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);_0x39c71c=(744003^744005)+(630601^630593);var _0x626gac=(918023^918031)+(561623^561622);let shift=j%that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0x626gac=186234^186239;let btValue=that['\u0062\u0074'][index];btValue=btValue|value<<shift;that['\u0062\u0074'][index]=btValue;}}that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']+(613228^613229);if(that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']>imageWidth){that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=imageWidth;}that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=imageWidth-that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D'];return that['\u0062\u0074'];},getBytes(nIndex){const that=this;let _0x816edg;let nCnt=Math['\u006D\u0069\u006E'](that['\u0077\u0069\u0064\u0074\u0068']-nIndex,318737^318736);_0x816edg='\u006F\u006C\u0071\u0068\u0067\u006D';if(nCnt<=(333649^333649)){return new Uint8Array();}let _0x974e;let cx=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u0068\u0065\u0069\u0067\u0068\u0074']+(289639^289632))/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);_0x974e=231907^231904;let _0xafg;let b=new Uint8Array(cx*nCnt);_0xafg=(163096^163097)+(907608^907608);for(var i=708607^708607;i<b['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){b[i]=that['\u0062\u0074'][nIndex*cx+i];}return b;},getBytesOne(nIndex,nCntNum){const that=this;let _0x7ae98e;let nCnt=Math['\u006D\u0069\u006E'](that['\u0077\u0069\u0064\u0074\u0068']-nIndex,nCntNum);_0x7ae98e="gmhppb".split("").reverse().join("");if(nCnt<=(751509^751509)){return new Uint8Array();}var _0x196a=(401807^401807)+(187312^187315);let cx=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u0068\u0065\u0069\u0067\u0068\u0074']+(453319^453312))/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);_0x196a="jkkjbo".split("").reverse().join("");var _0xc_0x2eb=(632905^632908)+(864050^864048);let b=new Uint8Array(cx*nCnt);_0xc_0x2eb='\u0067\u0064\u006B\u006D\u0066\u0069';for(var i=934938^934938;i<b['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){b[i]=that['\u0062\u0074'][nIndex*cx+i];}return b;},getBytesAll(){let _0x2g4bbg;const that=this;_0x2g4bbg=(822995^822998)+(265932^265925);return that['\u0062\u0074'];},getMarginTop(){const that=this;return that['\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070'];},getMarginBottom(){let _0xec7bc;const that=this;_0xec7bc=154619^154620;return that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D'];},getWidth(){return this['\u0068\u0065\u0069\u0067\u0068\u0074']/this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},getHeight(){return this['\u0077\u0069\u0064\u0074\u0068']/this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},getClean(){const that=this;that['\u0062\u0074']=[];that['\u0077\u0069\u0064\u0074\u0068']=285580^285580;that['\u0068\u0065\u0069\u0067\u0068\u0074']=216288^216288;that['\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']=410975^410975;that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=279136^279136;}};
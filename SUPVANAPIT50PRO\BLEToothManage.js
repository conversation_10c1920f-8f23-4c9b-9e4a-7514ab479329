import supPrintUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import canvasDataRGBAUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0061\u0074\u0061\u0052\u0047\u0042\u0041\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import barCodeUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import imageEncodeUtilsT50Pro from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0054\u0035\u0030\u0050\u0072\u006F\u002E\u006A\u0073";import imageEncodeUtilsMp50 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import drawQrcode from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0077\u0065\u0061\u0070\u0070\u002E\u0071\u0072\u0063\u006F\u0064\u0065\u002E\u0065\u0073\u006D\u002E\u006A\u0073";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";import bleTool from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C\u002E\u006A\u0073";import SupVanPrintUtilsMP50 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import SupVanPrintUtilsG15 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import imageEncodeUtilsG15 from"\u002E\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import SupVanPrintUtilsG21 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";import imageEncodeUtilsG21 from"\u002E\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";export default{"deviceSn":'',"barCodeData":{"width":'','\u0068\u0065\u0069\u0067\u0068\u0074':'',"path":''},"imagePath":'',"dataMeasure":{"width":'','\u0068\u0065\u0069\u0067\u0068\u0074':'','\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068':'','\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074':'','\u0071\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068':'',"qrcodeHeight":''},"qrCodeObject":{},'\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],'\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],'\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041':null,"canvasBarCode":null,'\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D':0,'\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068':400,"templateHeight":302,'\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C':null,"connectCallBack":null,"printType":constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058'],"dpiValue":8,async doPrintMatrix(myCanvasRGBA,nObjectData,canvasBarCode,callback){try{var _0xb45fb=(882188^882186)+(414681^414682);const that=this;_0xb45fb=(168616^168623)+(631823^631816);let objectData=nObjectData;if(!myCanvasRGBA){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0038'],"\u6587\u672C\u0043\u0061\u006E\u0076\u0061\u0073\u4E0D\u80FD\u4E3A\u7A7A");}if(constants['\u0069\u0073\u0041\u0072\u0072\u0061\u0079\u0045\u006D\u0070\u0074\u0079'](objectData)){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0036'],"\u6A21\u677F\u5BF9\u8C61\u4E0D\u80FD\u4E3A\u7A7A");}bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](160380^160372);if(bleTool['\u0065\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectData[413264^413264]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0071\u0075\u0065\u0072\u0079\u0047\u0031\u0035\u0044\u0070\u0069']());}else if(bleTool['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectData[541581^541581]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0072\u0065\u0061\u0064\u0047\u0032\u0031\u0044\u0070\u0069'](objectData[249512^249512]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']));}that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();console['\u006C\u006F\u0067']("\u0064\u0070\u0069\u0076\u0061\u006C\u0075\u0065",that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);that['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback,constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058']);that['\u006D\u0061\u0074\u0072\u0069\u0078\u0043\u006F\u0070\u0069\u0065\u0073'](myCanvasRGBA,objectData,canvasBarCode);var _0xd6c=(389217^389220)+(885334^885331);const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();_0xd6c=(689868^689871)+(426623^426614);callback(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065'],constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0030']));await that['\u006D\u0061\u0074\u0072\u0069\u0078\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async queryG15Dpi(){return new Promise(resolve=>{SupVanPrintUtilsG15['\u0071\u0075\u0065\u0072\u0079\u0044\u0050\u0049'](result=>{let dpi=result;let dpiValue=399468^399460;if(dpi>=(680664^680036)&&dpi<=(212013^212749)){dpiValue=dpi/(797356^797384);}resolve(dpiValue);});});},async readG21Dpi(deviceSn){return new Promise(resolve=>{SupVanPrintUtilsG21['\u0072\u0065\u0061\u0064\u0044\u0050\u0049'](result=>{console['\u006C\u006F\u0067']("tluser".split("").reverse().join(""),result);let dpi=result;var _0x9fcgc;let dpiValue=dpi/(944282^944382);_0x9fcgc=(343143^343143)+(422040^422032);let G28Device=bleTool['\u0067\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](deviceSn);if(G28Device){if(dpi<(575077^573993)||dpi>(805602^806482)){dpiValue=380705^380717;}}else{if(dpi<(299148^299568)||dpi>(699496^700232)){dpiValue=250472^250464;}}resolve(Math['\u0063\u0065\u0069\u006C'](dpiValue));});});},async printNextMatrix(){try{var _0xfd0cg=(491400^491392)+(629780^629783);const that=this;_0xfd0cg=(306074^306073)+(478604^478606);var _0xc35d3d=(227154^227156)+(670000^670005);const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();_0xc35d3d=(252262^252258)+(150616^150620);await that['\u006D\u0061\u0074\u0072\u0069\u0078\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async matrixCanvas(objectList){try{var _0x16a=(892885^892892)+(425922^425921);const that=this;_0x16a=965268^965269;var _0x9c645d;const objectData=await that['\u0071\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](objectList);_0x9c645d=849619^849622;await canvasDataRGBAUtils['\u006D\u0061\u0074\u0072\u0069\u0078\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},matrixCopies(myCanvasRGBA,objectData,canvasBarCode){const that=this;that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058'];that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061']();canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](false);that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']=myCanvasRGBA;that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']=canvasBarCode;that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];let objectListAllFirst=objectData['\u0073\u006C\u0069\u0063\u0065']();for(let object of objectListAllFirst){if(!object['\u0043\u006F\u0070\u0069\u0065\u0073']||object['\u0043\u006F\u0070\u0069\u0065\u0073']==(294306^294306)){object['\u0043\u006F\u0070\u0069\u0065\u0073']=855574^855575;}for(let i=217275^217275;i<object['\u0043\u006F\u0070\u0069\u0065\u0073'];i++){that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](object);}}that['\u0073\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'](objectData);},async doPrintImage(myCanvasRGBA,pageImageListData,callback){try{var _0xgc234c;const that=this;_0xgc234c=262350^262350;if(!myCanvasRGBA){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0038'],"\u6587\u672C\u0043\u0061\u006E\u0076\u0061\u0073\u4E0D\u80FD\u4E3A\u7A7A");}if(constants['\u0069\u0073\u0041\u0072\u0072\u0061\u0079\u0045\u006D\u0070\u0074\u0079'](pageImageListData)){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0036'],"\u6A21\u677F\u5BF9\u8C61\u4E0D\u80FD\u4E3A\u7A7A");}bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](550562^550570);if(bleTool['\u0065\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](pageImageListData[720164^720164]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0071\u0075\u0065\u0072\u0079\u0047\u0031\u0035\u0044\u0070\u0069']());}else if(bleTool['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](pageImageListData[178254^178254]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0072\u0065\u0061\u0064\u0047\u0032\u0031\u0044\u0070\u0069'](pageImageListData[562445^562445]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']));}that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();console['\u006C\u006F\u0067']("eulavipd".split("").reverse().join(""),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);that['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback,constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0049\u004D\u0041\u0047\u0045']);that['\u0069\u006D\u0061\u0067\u0065\u0043\u006F\u0070\u0069\u0065\u0073'](myCanvasRGBA,pageImageListData);var _0xe23gc=(326930^326930)+(771539^771538);const objectData=that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061']();_0xe23gc=(350622^350617)+(981178^981182);callback(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065'],constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0030']));await canvasDataRGBAUtils['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},async printNextImage(){try{var _0xe9g66g=(811589^811585)+(437462^437457);const that=this;_0xe9g66g=167992^167998;var _0xb_0xc9a=(242381^242381)+(525639^525633);const objectData=that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061']();_0xb_0xc9a="eqbkna".split("").reverse().join("");await canvasDataRGBAUtils['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},imageCopies(myCanvasRGBA,pageImageListData){const that=this;that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0049\u004D\u0041\u0047\u0045'];that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061']();canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](false);that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']=myCanvasRGBA;that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];let imageListAllFirst=pageImageListData['\u0073\u006C\u0069\u0063\u0065']();for(let object of imageListAllFirst){if(!object['\u0043\u006F\u0070\u0069\u0065\u0073']||object['\u0043\u006F\u0070\u0069\u0065\u0073']==(826066^826066)){object['\u0043\u006F\u0070\u0069\u0065\u0073']=515264^515265;}for(let i=613845^613845;i<object['\u0043\u006F\u0070\u0069\u0065\u0073'];i++){that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](object);}}that['\u0073\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'](pageImageListData);},async doDrawPreview(myCanvasRGBA,nObjectData,canvasBarCode,callback){try{const that=this;var _0x3a2a=(192821^192817)+(375554^375562);let objectData=nObjectData;_0x3a2a=(833159^833153)+(618898^618898);bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](388598^388606);that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();if(!myCanvasRGBA){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0038'],"\u7A7A\u4E3A\u80FD\u4E0DsavnaC\u672C\u6587".split("").reverse().join(""));}if(constants['\u0069\u0073\u0041\u0072\u0072\u0061\u0079\u0045\u006D\u0070\u0074\u0079'](objectData)){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0036'],"\u7A7A\u4E3A\u80FD\u4E0D\u8C61\u5BF9\u677F\u6A21".split("").reverse().join(""));}canvasDataRGBAUtils['\u0064\u0072\u0061\u0077\u0050\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);await that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u006F\u0070\u0069\u0065\u0073'](myCanvasRGBA,objectData,canvasBarCode);const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();callback(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065'],constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0030']));await that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async drawNextPreview(){try{const that=this;const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();await that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async previewCanvas(objectList){try{var _0x53ec;const that=this;_0x53ec=157095^157093;var _0xbb_0xg95=(812193^812196)+(134440^134447);const objectData=await that['\u0071\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](objectList);_0xbb_0xg95=(595269^595277)+(785464^785464);canvasDataRGBAUtils['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},async previewCopies(myCanvasRGBA,objectData,canvasBarCode){const that=this;that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0050\u0052\u0045\u0056\u0049\u0045\u0057'];that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061']();that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']=myCanvasRGBA;that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']=canvasBarCode;that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];var _0x9b33c=(804939^804936)+(459515^459516);let objectListAllFirst=objectData['\u0073\u006C\u0069\u0063\u0065']();_0x9b33c=(499935^499933)+(887369^887369);for(let object of objectListAllFirst){if(!object['\u0043\u006F\u0070\u0069\u0065\u0073']||object['\u0043\u006F\u0070\u0069\u0065\u0073']==(195924^195924)){object['\u0043\u006F\u0070\u0069\u0065\u0073']=781978^781979;}for(let i=589858^589858;i<object['\u0043\u006F\u0070\u0069\u0065\u0073'];i++){that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](object);}}that['\u0073\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'](objectData);},async analysisTemplateData(){try{var _0x7f_0x7aa=(214013^214014)+(153774^153772);const that=this;_0x7f_0x7aa=(792470^792469)+(300546^300555);let barcodeObject={};that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']={};that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']='';var _0x3f3c4f=(702987^702978)+(794916^794914);let objectList=that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();_0x3f3c4f="ibnfdj".split("").reverse().join("");that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']=objectList['\u0057\u0069\u0064\u0074\u0068'];that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=objectList['\u0048\u0065\u0069\u0067\u0068\u0074'];let drawObjects=objectList['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073'];for(let i in drawObjects){if(drawObjects[i]['\u0046\u006F\u0072\u006D\u0061\u0074']=="\u0042\u0041\u0052\u0043\u004F\u0044\u0045"){barcodeObject=drawObjects[i];}else if(drawObjects[i]['\u0046\u006F\u0072\u006D\u0061\u0074']=="EDOCRQ".split("").reverse().join("")){that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']=drawObjects[i];}else if(drawObjects[i]['\u0046\u006F\u0072\u006D\u0061\u0074']=="\u0049\u004D\u0041\u0047\u0045"){that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']=drawObjects[i]['\u0043\u006F\u006E\u0074\u0065\u006E\u0074'];}}if(that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']&&Object['\u006B\u0065\u0079\u0073'](that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074'])['\u006C\u0065\u006E\u0067\u0074\u0068']>(126672^126672)){that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']=that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];}if(barcodeObject&&Object['\u006B\u0065\u0079\u0073'](barcodeObject)['\u006C\u0065\u006E\u0067\u0074\u0068']>(653572^653572)&&that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']){const barCodeRes=await barCodeUtils['\u0067\u0065\u0074\u0042\u0061\u0072\u0063\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061'](barcodeObject,that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']);that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']=barCodeRes['\u0077\u0069\u0064\u0074\u0068'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=barCodeRes['\u0068\u0065\u0069\u0067\u0068\u0074'];var _0x4gfe7f=(387699^387707)+(915938^915937);let canvasId=barCodeRes['\u0063\u0061\u006E\u0076\u0061\u0073'];_0x4gfe7f='\u0070\u0068\u006F\u006C\u006F\u006F';const filePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'](canvasId);that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0070\u0061\u0074\u0068']=filePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'];that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0077\u0069\u0064\u0074\u0068']=barCodeRes['\u0077\u0069\u0064\u0074\u0068'];that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0068\u0065\u0069\u0067\u0068\u0074']=barCodeRes['\u0068\u0065\u0069\u0067\u0068\u0074'];}if(bleTool['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectList['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])||bleTool['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectList['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){objectList['\u004D\u0061\u0072\u0067\u0069\u006E']=980148^980150;if(objectList&&objectList['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']&&objectList['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(607458^607457)){let maxWidth=objectList['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];for(const textData of objectList['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073']){if(textData['\u0046\u006F\u0072\u006D\u0061\u0074']="\u0054\u0045\u0058\u0054"){that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0073\u0065\u0074\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065'](textData['\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);var _0x5729g=(693574^693582)+(795963^795963);const metrics=that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u006D\u0065\u0061\u0073\u0075\u0072\u0065\u0054\u0065\u0078\u0074'](textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']);_0x5729g=(228154^228158)+(220656^220656);var _0xcc1f7f;const currentWidth=metrics['\u0077\u0069\u0064\u0074\u0068'];_0xcc1f7f='\u0065\u006D\u0061\u0068\u0069\u006E';if(currentWidth>maxWidth){maxWidth=currentWidth;}textData['\u0057\u0069\u0064\u0074\u0068']=Math['\u0063\u0065\u0069\u006C'](maxWidth/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(421960^421963));}}objectList['\u0057\u0069\u0064\u0074\u0068']=Math['\u0063\u0065\u0069\u006C'](maxWidth/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(787080^787083));that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']=Math['\u0063\u0065\u0069\u006C'](maxWidth/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(305854^305853));}}that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0077\u0069\u0064\u0074\u0068']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0068\u0065\u0069\u0067\u0068\u0074']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];return objectList;}catch(error){throw error;}},imageData(){var _0x282b=(330279^330278)+(930422^930431);const that=this;_0x282b=880065^880073;var _0xf612g=(428731^428728)+(814840^814833);let imageListAll=that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();_0xf612g=(667665^667669)+(200662^200661);that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']=imageListAll['\u0057\u0069\u0064\u0074\u0068'];that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=imageListAll['\u0048\u0065\u0069\u0067\u0068\u0074'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0077\u0069\u0064\u0074\u0068']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0068\u0065\u0069\u0067\u0068\u0074']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']=imageListAll;that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']['\u004D\u0061\u0072\u0067\u0069\u006E']=507468^507470;return{'\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041':that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041'],"objectAll":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C'],"printNum":that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']};},async qrCodeData(objectList){var _0xd5411g=(884405^884403)+(292127^292124);const that=this;_0xd5411g=758904^758908;that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']=objectList;let qrFilePath='';if(that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']&&Object['\u006B\u0065\u0079\u0073'](that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074'])['\u006C\u0065\u006E\u0067\u0074\u0068']>(108496^108496)&&that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']){qrFilePath=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0051\u0072\u0043\u006F\u0064\u0065']();}return{"myCanvasRGBA":that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041'],'\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C'],'\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061':that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061'],"printNum":that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068'],'\u0071\u0072\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068':qrFilePath,"imagePath":that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'],'\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065':that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']};},async canvasQrCode(){try{var _0x5c7cca=(112472^112476)+(515965^515966);const that=this;_0x5c7cca=109209^109200;await new Promise(resolve=>setTimeout(resolve,629057^629107));drawQrcode({"width":that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0068\u0065\u0069\u0067\u0068\u0074':that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"canvasId":"\u0071\u0072\u0043\u006F\u0064\u0065","text":that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']});var _0xfe3ddf=(138054^138063)+(822407^822405);const qrfilePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068\u0051\u0072\u0043\u006F\u0064\u0065']();_0xfe3ddf=(323316^323312)+(962156^962154);let qrFilePath=qrfilePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'];console['\u006C\u006F\u0067']("htaPegami".split("").reverse().join(""),that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']);if(that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']){const downLoadFileRes=await that['\u0064\u006F\u0077\u006E\u006C\u006F\u0061\u0064\u0046\u0069\u006C\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']);that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']=downLoadFileRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'];}return qrFilePath;}catch(error){throw error;}},downloadFile(url){try{return constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0064\u006F\u0077\u006E\u006C\u006F\u0061\u0064\u0046\u0069\u006C\u0065'],{"url":url});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0031'],error);}},canvasToTempFilePathQrCode(){try{return constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'],{'\u0063\u0061\u006E\u0076\u0061\u0073\u0049\u0064':'qrCode','\u0066\u0069\u006C\u0065\u0054\u0079\u0070\u0065':"\u0070\u006E\u0067","quality":1.0});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0030'],error);}},canvasToTempFilePath(canvasId){return new Promise((resolve,reject)=>{wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']({'\u0063\u0061\u006E\u0076\u0061\u0073':canvasId,'\u0066\u0069\u006C\u0065\u0054\u0079\u0070\u0065':'png','\u0071\u0075\u0061\u006C\u0069\u0074\u0079':1.0,'\u0073\u0075\u0063\u0063\u0065\u0073\u0073':res=>{console['\u006C\u006F\u0067']("\u751F\u6210\u56FE\u7247\u6210\u529F",res);resolve(res);},"fail":error=>{console['\u006C\u006F\u0067']("\u8D25\u5931\u7247\u56FE\u6210\u751F".split("").reverse().join(""),error);reject(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0039'],error));}},this);});},getImageAllNum(){return this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'];},setImageAllNum(nObjectData){var _0xd631eg=(152681^152673)+(377820^377813);let objectData=nObjectData;_0xd631eg="cbikcl".split("").reverse().join("");this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']=569430^569430;for(let data of objectData){this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']=Number(this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'])+Number(data['\u0043\u006F\u0070\u0069\u0065\u0073']);}},cleanBarcodeData(){this['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0077\u0069\u0064\u0074\u0068']='';this['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0068\u0065\u0069\u0067\u0068\u0074']='';this['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0070\u0061\u0074\u0068']='';},cleanDataMeasure(){this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0077\u0069\u0064\u0074\u0068']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0068\u0065\u0069\u0067\u0068\u0074']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']='';},cleanData(){const that=this;canvasDataRGBAUtils['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074']();imageEncodeUtilsT50Pro['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();imageEncodeUtilsMp50['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();imageEncodeUtilsG15['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();imageEncodeUtilsG21['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();that['\u0063\u006C\u0065\u0061\u006E\u0042\u0061\u0072\u0063\u006F\u0064\u0065\u0044\u0061\u0074\u0061']();that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']();},printCallback(nCallBack,nType){var _0x36da3e;let callBack=nCallBack;_0x36da3e=(352682^352674)+(535770^535772);let type=nType;supPrintUtils['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);SupVanPrintUtilsMP50['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);SupVanPrintUtilsG15['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);SupVanPrintUtilsG21['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsT50Pro['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsMp50['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsG15['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsG21['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);canvasDataRGBAUtils['\u0064\u0074\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);},async ConsumableInformation(){return new Promise(resolve=>{supPrintUtils['\u0072\u0065\u0061\u0064\u0043\u006F\u006E\u0073\u0075\u006D\u0061\u0062\u006C\u0065\u004D\u0065\u0073\u0073\u0061\u0067\u0065'](result=>{resolve(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](result));});});}};
import imageEncodeUtilsT50Pro from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0054\u0035\u0030\u0050\u0072\u006F\u002E\u006A\u0073";import imageEncodeUtilsMP50 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import imageEncodeUtilsG15 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import imageEncodeUtilsG21 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";import bleToothManage from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065\u002E\u006A\u0073";import bleTool from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C\u002E\u006A\u0073";import lpapi from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u004C\u0050\u0041\u0050\u0049\u002F\u004C\u0050\u0041\u0050\u0049\u002E\u006A\u0073";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";export default{"myCanvasRGBA":null,"pixelRatio":1,'\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065':8,'\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C':{},'\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074':[],"imageObjectDT":[],"previewCallBack":null,'\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B':null,'\u0064\u0074\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B':null,'\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065':'','\u0063\u0061\u006E\u0063\u0065\u006C\u0044\u0074\u0050\u0072\u0069\u006E\u0074':false,"barCodeObject":null,'\u0071\u0072\u0043\u006F\u0064\u0065\u0050\u0061\u0074\u0068':'',"imagePath":'',async matrixDataCanvas(nObjectData){try{var _0xf7e2f=(446408^446413)+(547358^547358);const that=this;_0xf7e2f=118198^118195;var _0x569b=(805135^805132)+(140308^140306);let objectData=nObjectData;_0x569b=(924532^924528)+(774955^774959);that['\u0064\u0061\u0074\u0061\u0049\u006E\u0069\u0074'](objectData);for(let i in that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073']){console['\u006C\u006F\u0067']("lacoLeulaVtcejbo.taht".split("").reverse().join(""),that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']);that['\u0064\u0072\u0061\u0077\u0053\u0077\u0069\u0074\u0063\u0068\u0046\u006F\u0072\u006D\u0061\u0074'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073'][i]);}await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();await that['\u006D\u0061\u0074\u0072\u0069\u0078\u0043\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();}catch(error){throw error;}},async previewDataCanvas(nObjectData){try{var _0xc6d=(127766^127762)+(126861^126860);const that=this;_0xc6d=537634^537642;let objectData=nObjectData;that['\u0064\u0061\u0074\u0061\u0049\u006E\u0069\u0074'](objectData);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0050\u0072\u0065\u0076\u0069\u0065\u0077\u0050\u0061\u0074\u0068']){await that['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0050\u0072\u0065\u0076\u0069\u0065\u0077\u0050\u0061\u0074\u0068']);}for(let i in objectData['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073']){that['\u0064\u0072\u0061\u0077\u0053\u0077\u0069\u0074\u0063\u0068\u0046\u006F\u0072\u006D\u0061\u0074'](objectData['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073'][i]);}await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();}catch(error){throw error;}},async dataInit(nObjectData){const that=this;let objectData=nObjectData;let res=wx['\u0067\u0065\u0074\u0057\u0069\u006E\u0064\u006F\u0077\u0049\u006E\u0066\u006F']();that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F']=res['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'];that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']=objectData['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061'];that['\u0071\u0072\u0043\u006F\u0064\u0065\u0050\u0061\u0074\u0068']=objectData['\u0071\u0072\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'];that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']=objectData['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'];that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']=objectData['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041'];that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']=objectData['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C'];that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0063\u006C\u0065\u0061\u0072\u0052\u0065\u0063\u0074'](712586^712586,785244^785244,that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F']);},async drawImagePath(path){try{const that=this;var _0xdae69d=(135951^135951)+(493256^493260);const loadFileRes=await that['\u0064\u006F\u0077\u006E\u006C\u006F\u0061\u0064\u0046\u0069\u006C\u0065'](path);_0xdae69d=(340970^340970)+(239481^239487);if(loadFileRes){that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065'](loadFileRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'],(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0049\u006D\u0061\u0067\u0065\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'])/(561140^561142),(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0049\u006D\u0061\u0067\u0065\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'])/(241774^241772),that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0049\u006D\u0061\u0067\u0065\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0049\u006D\u0061\u0067\u0065\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);}}catch(error){throw error;}},async imageDataCanvas(nObjectData){try{var _0xa097db=(738723^738727)+(404483^404482);const that=this;_0xa097db="gmmbki".split("").reverse().join("");var _0x8e1c7f;let objectData=nObjectData;_0x8e1c7f=(777020^777020)+(721489^721490);that['\u0064\u0061\u0074\u0061\u0049\u006E\u0069\u0074'](objectData);await that['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0049\u006D\u0061\u0067\u0065\u0055\u0072\u006C']);await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();await that['\u0069\u006D\u0061\u0067\u0065\u0043\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();}catch(error){throw error;}},drawSwitchFormat(nMatrixObject){const that=this;let matrixObject=nMatrixObject;switch(matrixObject['\u0046\u006F\u0072\u006D\u0061\u0074']){case"EDOCRAB".split("").reverse().join(""):var _0xe3c4af=(204165^204164)+(285466^285470);let barCodeX=matrixObject['\u0058']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0xe3c4af=804593^804599;var _0x04ead=(227502^227495)+(390618^390618);let barCodeY=matrixObject['\u0059']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0x04ead=374877^374873;if(that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0070\u0061\u0074\u0068']){that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0069\u006D\u0061\u0067\u0065\u0053\u006D\u006F\u006F\u0074\u0068\u0069\u006E\u0067\u0045\u006E\u0061\u0062\u006C\u0065\u0064']=false;that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065'](that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0070\u0061\u0074\u0068'],barCodeX,barCodeY,that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0077\u0069\u0064\u0074\u0068'],that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0068\u0065\u0069\u0067\u0068\u0074']);}break;case"EDOCRQ".split("").reverse().join(""):if(that['\u0071\u0072\u0043\u006F\u0064\u0065\u0050\u0061\u0074\u0068']){that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0069\u006D\u0061\u0067\u0065\u0053\u006D\u006F\u006F\u0074\u0068\u0069\u006E\u0067\u0045\u006E\u0061\u0062\u006C\u0065\u0064']=false;let qrCodeX=matrixObject['\u0058']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];var _0x1e58d=(995493^995491)+(171520^171521);let qrCodeY=matrixObject['\u0059']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0x1e58d=(597270^597270)+(163421^163423);let qrWidth=matrixObject['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];let qrHeight=matrixObject['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065'](that['\u0071\u0072\u0043\u006F\u0064\u0065\u0050\u0061\u0074\u0068'],qrCodeX,qrCodeY,qrWidth,qrHeight);}break;case"EGAMI".split("").reverse().join(""):if(that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']){that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'],matrixObject['\u0058']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],matrixObject['\u0059']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],matrixObject['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],matrixObject['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);}break;case"\u0054\u0045\u0058\u0054":that['\u0064\u0072\u0061\u0077\u0054\u0065\u0078\u0074'](that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041'],matrixObject);break;}},async downloadFile(url){try{return await constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0064\u006F\u0077\u006E\u006C\u006F\u0061\u0064\u0046\u0069\u006C\u0065'],{'\u0075\u0072\u006C':url});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0033'],error);}},canvasDraw(){return new Promise((resolve,reject)=>{var _0x73117d;const that=this;_0x73117d=960003^960010;that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0073\u0063\u0061\u006C\u0065'](that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'],that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F']);that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0064\u0072\u0061\u0077'](false,()=>{setTimeout(()=>{resolve();},337431^337723);});});},async matrixCanvasDraw(){try{const that=this;if(bleTool['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){var _0x394b6b;const filePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']();_0x394b6b=334823^334822;for(let index=379217^379217;index<that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0043\u006F\u0070\u0069\u0065\u0073'];index++){that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u0070\u0075\u0073\u0068'](filePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']);}if(that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u006C\u0065\u006E\u0067\u0074\u0068']<bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()){await bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u004D\u0061\u0074\u0072\u0069\u0078']();}else{await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u0044\u0054']();}}else{const imageRgbaRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0047\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061']();await that['\u006D\u006F\u0064\u0065\u006C\u0049\u006E\u0064\u0065\u006E\u0074\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006F\u006E'](imageRgbaRes);}}catch(error){throw error;}},async modelIndentification(imageRgbaRes){try{const that=this;if(bleTool['\u0074\u0035\u0030\u0050\u0072\u006F\u0041\u006E\u0064\u0054\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await imageEncodeUtilsT50Pro['\u0069\u006E\u0069\u0074\u0045\u006E\u0063\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C'],imageRgbaRes['\u0064\u0061\u0074\u0061']);}else if(bleTool['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await imageEncodeUtilsMP50['\u0069\u006E\u0069\u0074\u0045\u006E\u0063\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C'],imageRgbaRes['\u0064\u0061\u0074\u0061']);}else if(bleTool['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await imageEncodeUtilsG15['\u0069\u006E\u0069\u0074\u0045\u006E\u0063\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C'],imageRgbaRes['\u0064\u0061\u0074\u0061']);}else if(bleTool['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await imageEncodeUtilsG21['\u0069\u006E\u0069\u0074\u0045\u006E\u0063\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C'],imageRgbaRes['\u0064\u0061\u0074\u0061']);}}catch(error){throw error;}},async previewCanvasDraw(){try{const that=this;const filePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']();that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0070\u0075\u0073\u0068'](filePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']);if(that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u006C\u0065\u006E\u0067\u0074\u0068']<bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()){bleToothManage['\u0064\u0072\u0061\u0077\u004E\u0065\u0078\u0074\u0050\u0072\u0065\u0076\u0069\u0065\u0077']();}else{that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({"previewList":that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074']}));}}catch(error){throw error;}},async imageCanvasDraw(){try{const that=this;if(bleTool['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){const filePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']();for(let index=865846^865846;index<that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0043\u006F\u0070\u0069\u0065\u0073'];index++){that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u0070\u0075\u0073\u0068'](filePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']);}if(that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u006C\u0065\u006E\u0067\u0074\u0068']<bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()){await bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0049\u006D\u0061\u0067\u0065']();}else{await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u0044\u0054']();}}else{var _0x1081e=(948590^948584)+(675767^675762);const imageRgbaRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0047\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061']();_0x1081e=218254^218251;await that['\u006D\u006F\u0064\u0065\u006C\u0049\u006E\u0064\u0065\u006E\u0074\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006F\u006E'](imageRgbaRes);}}catch(error){throw error;}},drawText(ctx,textData){var _0x3a1b=(384230^384226)+(918271^918263);const that=this;_0x3a1b=702465^702470;let mFontSize=117559^117559;let mAutoReturnType=false;let zoom=408863^408862;ctx['\u0073\u0061\u0076\u0065']();ctx['\u0069\u006D\u0061\u0067\u0065\u0053\u006D\u006F\u006F\u0074\u0068\u0069\u006E\u0067\u0045\u006E\u0061\u0062\u006C\u0065\u0064']=false;var _0xe36af=(885160^885167)+(480310^480307);let fontSize=textData['\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0xe36af=678166^678166;var _0x122a7f=(505024^505033)+(302421^302416);let fontWeight="004".split("").reverse().join("");_0x122a7f=(595467^595458)+(690611^690618);if(textData['\u0046\u006F\u006E\u0074\u0053\u0074\u0079\u006C\u0065']==(872033^872035)){fontWeight="\u0062\u006F\u006C\u0064";}ctx['\u0066\u006F\u006E\u0074']=`normal ${fontWeight} ${fontSize}px sans-serif`;if(textData['\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065']==(641234^641234)){mFontSize=470167^470164;}else{ctx['\u0073\u0065\u0074\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065'](textData['\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);mFontSize=textData['\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065'];}let textY=textData['\u0059']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(textData['\u0048\u0065\u0069\u0067\u0068\u0074']-mFontSize)/(148214^148212)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+mFontSize*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];const metrics=ctx['\u006D\u0065\u0061\u0073\u0075\u0072\u0065\u0054\u0065\u0078\u0074'](textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']);const zoomScal=metrics['\u0077\u0069\u0064\u0074\u0068']/(textData['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);console['\u006C\u006F\u0067']("\u0074\u0065\u0078\u0074\u0044\u0061\u0074\u0061\u002E\u0043\u006F\u006E\u0074\u0065\u006E\u0074",textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']);console['\u006C\u006F\u0067']("htdiw.scirtem".split("").reverse().join(""),metrics);console['\u006C\u006F\u0067']("\u0074\u0065\u0078\u0074\u0044\u0061\u0074\u0061\u002E\u0057\u0069\u0064\u0074\u0068",textData['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);console['\u006C\u006F\u0067']("\u007A\u006F\u006F\u006D\u0053\u0063\u0061\u006C",zoomScal);if(textData['\u0041\u0075\u0074\u006F\u0052\u0065\u0074\u0075\u0072\u006E']){zoom=274419^274418;if(zoomScal>1.1){textY=textData['\u0059']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(textData['\u0048\u0065\u0069\u0067\u0068\u0074']-mFontSize*(976839^976837))/(214098^214096)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+mFontSize*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];mAutoReturnType=!![];}}else{if(zoomScal>(890094^890095)){zoom=textData['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']/metrics['\u0077\u0069\u0064\u0074\u0068'];}else{zoom=860469^860468;}}ctx['\u0074\u0065\u0078\u0074\u0042\u0061\u0073\u0065\u006C\u0069\u006E\u0065']="\u0069\u0064\u0065\u006F\u0067\u0072\u0061\u0070\u0068\u0069\u0063";console['\u006C\u006F\u0067']("epyTrepap".split("").reverse().join(""),that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']);console['\u006C\u006F\u0067']("\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C\u002E\u0057\u0069\u0064\u0074\u0068",that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']);console['\u006C\u006F\u0067']("\u0074\u0065\u0078\u0074\u0044\u0061\u0074\u0061\u002E\u0057\u0069\u0064\u0074\u0068",textData['\u0057\u0069\u0064\u0074\u0068']);console['\u006C\u006F\u0067']("mooz".split("").reverse().join(""),zoom);ctx['\u0073\u0063\u0061\u006C\u0065'](zoom,566709^566708);var _0xe5c34a;let textX=664256^664256;_0xe5c34a=(394309^394309)+(159259^159260);if(textData['\u004F\u0072\u0069\u0065\u006E\u0074\u0061\u0074\u0069\u006F\u006E']==(885276^885277)){ctx['\u0073\u0065\u0074\u0054\u0065\u0078\u0074\u0041\u006C\u0069\u0067\u006E']("\u0063\u0065\u006E\u0074\u0065\u0072");textX=(textData['\u0058']+textData['\u0057\u0069\u0064\u0074\u0068']/(487909^487911))*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']/zoom;}else{ctx['\u0073\u0065\u0074\u0054\u0065\u0078\u0074\u0041\u006C\u0069\u0067\u006E']("\u006C\u0065\u0066\u0074");textX=textData['\u0058']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']/zoom;}if(!textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']){return;}if(mAutoReturnType){let objectData={"ctx":ctx,'\u0063\u006F\u006E\u0074\u0065\u006E\u0074':textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074'],'\u0064\u0072\u0061\u0077\u0058':textX,"drawY":textY,'\u006C\u0069\u006E\u0065\u0048\u0065\u0069\u0067\u0068\u0074':(textData['\u0048\u0065\u0069\u0067\u0068\u0074']+(401112^401113))/(575612^575614)*this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u006C\u0069\u006E\u0065\u004D\u0061\u0078\u0057\u0069\u0064\u0074\u0068':(textData['\u0057\u0069\u0064\u0074\u0068']-0.5)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u006C\u0069\u006E\u0065\u004E\u0075\u006D':2,"isTitle":false};this['\u0075\u0073\u0065\u0054\u0065\u0078\u0074\u0050\u0072\u0065\u0077\u0072\u0061\u0070'](objectData);}else{console['\u006C\u006F\u0067']("\u0043\u006F\u006E\u0074\u0065\u006E\u0074",textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']);ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074'],textX,textY);}ctx['\u0072\u0065\u0073\u0074\u006F\u0072\u0065']();},useTextPrewrap(objectData){let ctx=objectData['\u0063\u0074\u0078'];var _0x5e7ab=(212158^212150)+(773178^773181);let content=objectData['\u0063\u006F\u006E\u0074\u0065\u006E\u0074'];_0x5e7ab=(594013^594011)+(438022^438017);let drawX=objectData['\u0064\u0072\u0061\u0077\u0058'];var _0xbddbdg=(692462^692454)+(350892^350893);let drawY=objectData['\u0064\u0072\u0061\u0077\u0059'];_0xbddbdg="jbihbi".split("").reverse().join("");let lineHeight=objectData['\u006C\u0069\u006E\u0065\u0048\u0065\u0069\u0067\u0068\u0074'];var _0x94g7b=(996780^996776)+(851874^851878);let lineMaxWidth=objectData['\u006C\u0069\u006E\u0065\u004D\u0061\u0078\u0057\u0069\u0064\u0074\u0068'];_0x94g7b=(351210^351211)+(530292^530291);var _0x97c=(319412^319414)+(180741^180749);let lineNum=objectData['\u006C\u0069\u006E\u0065\u004E\u0075\u006D'];_0x97c=(923971^923971)+(961063^961071);var _0x82be0a=(293905^293910)+(650042^650040);let isTitle=objectData['\u0069\u0073\u0054\u0069\u0074\u006C\u0065'];_0x82be0a=351580^351573;var drawTxt='';var drawLine=684668^684669;var _0xdd9ag=(777688^777691)+(726719^726712);var drawIndex=805572^805572;_0xdd9ag=(169590^169589)+(420868^420871);let reg=new RegExp('\u005B\u005C\u0075\u0033\u0030\u0030\u0032\u007C\u005C\u0075\u0066\u0066\u0031\u0066\u007C\u005C\u0075\u0066\u0066\u0030\u0031\u007C\u005C\u0075\u0066\u0066\u0030\u0063\u007C\u005C\u0075\u0033\u0030\u0030\u0031\u007C\u005C\u0075\u0066\u0066\u0031\u0062\u007C\u005C\u0075\u0066\u0066\u0031\u0061\u007C\u005C\u0075\u0032\u0030\u0031\u0064\u007C\u005C\u0075\u0032\u0030\u0031\u0039\u007C\u005C\u0075\u0066\u0066\u0030\u0039\u007C\u005C\u0075\u0033\u0030\u0030\u0062\u007C\u005C\u0075\u0033\u0030\u0030\u0039\u007C\u005C\u0075\u0033\u0030\u0031\u0031\u007C\u005C\u0075\u0033\u0030\u0030\u0066\u007C\u005C\u0075\u0033\u0030\u0030\u0064\u007C\u005C\u0075\u0033\u0030\u0031\u0035\u007C\u005C\u0075\u0032\u0030\u0032\u0036\u007C\u005C\u0075\u0066\u0066\u0065\u0035\u005D',"");if(ctx['\u006D\u0065\u0061\u0073\u0075\u0072\u0065\u0054\u0065\u0078\u0074'](content)['\u0077\u0069\u0064\u0074\u0068']<=lineMaxWidth){ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](content['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](drawIndex,i),drawX,drawY);}else{for(var i=338913^338913;i<content['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){drawTxt+=content[i];if(content[i]==="\u000A"){ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](content['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](drawIndex,i+(346746^346747)),drawX,drawY);drawIndex=i+(604715^604714);drawLine+=851779^851778;drawY+=lineHeight;drawTxt='';if(drawLine>=lineNum){break;}else{continue;}};if(ctx['\u006D\u0065\u0061\u0073\u0075\u0072\u0065\u0054\u0065\u0078\u0074'](drawTxt)['\u0077\u0069\u0064\u0074\u0068']>=lineMaxWidth&&!reg['\u0074\u0065\u0073\u0074'](content[i+(930522^930523)])&&i!==content['\u006C\u0065\u006E\u0067\u0074\u0068']-(365324^365325)){if(drawLine>=lineNum){ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](content['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](drawIndex,i+(680558^680559))+"\u002E\u002E",drawX,drawY);break;}else{ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](content['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](drawIndex,i+(831587^831586)),drawX,drawY);drawIndex=i+(583811^583810);if(content[i+(981687^981686)]!=="\u000A"){drawLine+=569543^569542;drawY+=lineHeight;}drawTxt='';}}else{if(i===content['\u006C\u0065\u006E\u0067\u0074\u0068']-(478980^478981)){ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](content['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](drawIndex),drawX,drawY);}}}}},async canvasGetImageData(){try{var _0x32f2cf;const that=this;_0x32f2cf="cicqeq".split("").reverse().join("");console['\u006C\u006F\u0067']("htdiW".split("").reverse().join(""),that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']);console['\u006C\u006F\u0067']("thgieH".split("").reverse().join(""),that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']);console['\u006C\u006F\u0067']("\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065",that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);let objectData={'\u0063\u0061\u006E\u0076\u0061\u0073\u0049\u0064':'Canvas','\u0078':0,'\u0079':0,'\u0077\u0069\u0064\u0074\u0068':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0068\u0065\u0069\u0067\u0068\u0074':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']};return await constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0047\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061'],objectData);}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0032'],error);}},async canvasToTempFilePath(){try{const that=this;var _0x6506a;let objectData={'\u0078':0,'\u0079':0,"width":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0068\u0065\u0069\u0067\u0068\u0074':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0064\u0065\u0073\u0074\u0057\u0069\u0064\u0074\u0068':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'],'\u0064\u0065\u0073\u0074\u0048\u0065\u0069\u0067\u0068\u0074':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'],'\u0063\u0061\u006E\u0076\u0061\u0073\u0049\u0064':"\u0043\u0061\u006E\u0076\u0061\u0073","fileType":'png','\u0071\u0075\u0061\u006C\u0069\u0074\u0079':1.0};_0x6506a=(421338^421331)+(781428^781437);return await constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'],objectData);}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0034'],error);}},async doPrintDT(){try{const that=this;lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0053\u0070\u0065\u0065\u0064'](396917^396917);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(991263^991262)){lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0050\u0061\u0067\u0065\u0047\u0061\u0070\u0054\u0079\u0070\u0065'](113227^113225);}else if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(605478^605475)){lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0050\u0061\u0067\u0065\u0047\u0061\u0070\u0054\u0079\u0070\u0065'](695916^695919);}else{lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0050\u0061\u0067\u0065\u0047\u0061\u0070\u0054\u0079\u0070\u0065'](508764^508766);}lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0044\u0061\u0072\u006B\u006E\u0065\u0073\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u006E\u0073\u0069\u0074\u0079']);lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0047\u0072\u0061\u0079\u0054\u0068\u0072\u0065\u0073\u0068\u006F\u006C\u0064'](234761^234953);let widthDt=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068'];let heightDt=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074'];var _0x3e8ccb=(556090^556091)+(829840^829847);let horizontalNumDt=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D'];_0x3e8ccb="jobkbo".split("").reverse().join("");var _0xd0bd=(351157^351152)+(882856^882849);let verticalNumDt=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D'];_0xd0bd=(405951^405945)+(823291^823283);let rotate=823609^823609;if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0052\u006F\u0074\u0061\u0074\u0065']==(687179^687178)){heightDt=heightDt-(736966^736964);verticalNumDt=verticalNumDt+(277823^277822);rotate=973327^973327;}else{widthDt=widthDt-(466143^466141);horizontalNumDt=horizontalNumDt+(455732^455733);rotate=532960^532922;}lpapi['\u0073\u0074\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u004C\u0061\u0062\u0065\u006C'](widthDt,heightDt,rotate);let dtImg=this['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u0073\u0068\u0069\u0066\u0074']();lpapi['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'](dtImg,horizontalNumDt,verticalNumDt,widthDt,heightDt,function(){that['\u0070\u0072\u0069\u006E\u0074\u004F\u006E\u0065\u004C\u0061\u0062\u0065\u006C']();});}catch(error){throw error;}},async printOneLabel(){try{const that=this;lpapi['\u0070\u0072\u0069\u006E\u0074'](function(){that['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u004C\u0061\u0062\u0065\u006C']();});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0035'],error);}},async printNextLabel(){try{const that=this;if(that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u006C\u0065\u006E\u0067\u0074\u0068']==(395251^395251)){this['\u0064\u0074\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({"msg":"\u5168\u90E8\u6253\u5370\u5B8C\u6210"}));}else{if(!that['\u0063\u0061\u006E\u0063\u0065\u006C\u0044\u0074\u0050\u0072\u0069\u006E\u0074']){await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u0044\u0054']();}else{this['\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':'终止打印成功'}));}}}catch(error){throw error;}},stopPrintDt(dtPrint){const that=this;that['\u0063\u0061\u006E\u0063\u0065\u006C\u0044\u0074\u0050\u0072\u0069\u006E\u0074']=dtPrint;},cleanImageObject(){this['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074']=[];this['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']=[];},drawPreviewCallback(previewCallBack){this['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=previewCallBack;},stopPrintCallback(stopCallBack){this['\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=stopCallBack;},dtPrintSuccessCallback(dtPrintSuccessCallBack,type){this['\u0064\u0074\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=dtPrintSuccessCallBack;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;}};
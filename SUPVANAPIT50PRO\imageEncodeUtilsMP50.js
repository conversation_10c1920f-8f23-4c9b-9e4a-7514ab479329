import printingControl from"\u002E\u002F\u0050\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0043\u006F\u006E\u0074\u0072\u006F\u006C\u002E\u006A\u0073";import pr from"\u002E\u002F\u0050\u0041\u0047\u0045\u005F\u0052\u0045\u0047\u005F\u0042\u0049\u0054\u0053\u002E\u006A\u0073";import lzma from"\u002E\u002F\u006C\u007A\u006D\u0061\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import imageDataUtils from"\u002E\u002F\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0055\u0074\u0069\u006C\u0073";import supVanPrintUtilsMP50 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import bleToothManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065";import bleTool from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C";import constants from"\u002E\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";export default{'\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068':50,"matHeight":30,'\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065':12,'\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061':[],'\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074':[],"overturnType":0,'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],"imageDataListAllTotalCnt":0,'\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065':false,'\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074':[],"objectData":null,'\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065':'',"callback":null,"maxDotValue":384,async initEncodeData(objectData,imageRgbaData){try{console['\u006C\u006F\u0067']("05PMslitUedocnEegami".split("").reverse().join(""));let _0x005bg;const that=this;_0x005bg='\u006E\u0061\u0070\u006D\u0064\u0066';that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();if(that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']==(206208^206216)){that['\u006D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065']=356969^357353;}else{that['\u006D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065']=453878^454326;}that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']=objectData;that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061']=imageRgbaData;that['\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065']=objectData['\u0069\u0073\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E']?195503^195502:261575^261575;await that['\u0069\u006E\u0069\u0074\u0069\u0061\u006C\u0069\u007A\u0065\u0044\u0061\u0074\u0061']();await that['\u0069\u006E\u0069\u0074\u004C\u005A\u004D\u0041\u0044\u0061\u0074\u0061']();await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074']();}catch(error){throw error;}},initializeData(){const that=this;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];printingControl['\u0063\u006C\u0065\u0061\u006E\u0050\u0063']();imageDataUtils['\u0067\u0065\u0074\u0043\u006C\u0065\u0061\u006E']();pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']==(695519^695519)){that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']=766816^766817;}var _0x0d76a=(477701^477702)+(148113^148121);let rotateAngle=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']-(978760^978762))*(116064^116026);_0x0d76a=305126^305126;let object={"imageRgbaData":that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061'],"Width":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0048\u0065\u0069\u0067\u0068\u0074':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0072\u006F\u0074\u0061\u0074\u0065\u0041\u006E\u0067\u006C\u0065':rotateAngle,'\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']*(376728^376732),'\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D']*(447165^447161),"overturnType":that['\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065'],'\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']};console['\u006C\u006F\u0067']("\u006F\u0062\u006A\u0065\u0063\u0074",object);imageDataUtils['\u0067\u0065\u0074\u0041\u006C\u006C\u0042\u0079\u0074\u0065\u0073'](object);that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']=imageDataUtils['\u0067\u0065\u0074\u0057\u0069\u0064\u0074\u0068']();that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']=imageDataUtils['\u0067\u0065\u0074\u0048\u0065\u0069\u0067\u0068\u0074']();printingControl['\u0043\u006F\u006C\u0075\u006D\u006E\u004C\u0065\u0066\u0074']=that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},async initLZMAData(){try{let _0x29c;const that=this;_0x29c=(440562^440563)+(756627^756635);let _0x_0xc2b;let bufferTransferCount=633739^633739;_0x_0xc2b=(875715^875714)+(812420^812416);let num=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0043\u006F\u0070\u0069\u0065\u0073'];let _0xdd21f;let _bufLength=839371^843467;_0xdd21f='\u0067\u006F\u0067\u0064\u0067\u0067';let _0x15f61e;let _maxBufLength=202298^210490;_0x15f61e=(934064^934071)+(664323^664322);var _0x4cf=(638485^638480)+(647434^647426);let countBuff=new Array();_0x4cf=(461754^461746)+(748814^748815);let isEndFlag=!![];let imgTotalCount=235356^235357;let _0x15bd;let marginleft=Math['\u006D\u0061\u0078'](imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']()-(814433^814432),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);_0x15bd=878747^878739;let marginright=Math['\u006D\u0061\u0078'](imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D'](),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);console['\u006C\u006F\u0067']("\u5DE6\u8FB9\u8DDD",marginleft);console['\u006C\u006F\u0067']("\u53F3\u8FB9\u8DDD",marginright);console['\u006C\u006F\u0067']("\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074",that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']);var _0x8f9ac=(821204^821202)+(195283^195280);let _nColumnTotalCnt=that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginleft-marginright;_0x8f9ac=(376048^376050)+(124646^124655);console['\u006C\u006F\u0067']("\u6570\u5217\u7247\u56FE".split("").reverse().join(""),_nColumnTotalCnt);console['\u006C\u006F\u0067']("eulaVipd".split("").reverse().join(""),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);console['\u006C\u006F\u0067']("eulaVtoDxam".split("").reverse().join(""),that['\u006D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065']);let _0xf6a8fa;let nBytePerLine=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u006D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065']+(850628^850627))/(493054^493046));_0xf6a8fa=(647426^647428)+(103538^103543);console['\u006C\u006F\u0067']("\u6BCF\u5217\u5B57\u8282\u6570",nBytePerLine);let nMax=Math['\u0066\u006C\u006F\u006F\u0072']((_bufLength-(487766^487744))/nBytePerLine);console['\u006C\u006F\u0067']("\u6BCF\u4E2A\u7F13\u51B2\u533A\u6700\u5927\u5217\u6570",nMax);let bufferCountImage=Math['\u0066\u006C\u006F\u006F\u0072']((_nColumnTotalCnt+nMax-(255216^255217))/nMax);console['\u006C\u006F\u0067']("\u6BCF\u4E2A\u56FE\u7247\u7F13\u51B2\u533A\u6570\u91CF\u0020\u0062\u0075\u0066\u0066\u0065\u0072\u0043\u006F\u0075\u006E\u0074\u0049\u006D\u0061\u0067\u0065",bufferCountImage);let _0xd22a;let allBytes=imageDataUtils['\u0067\u0065\u0074\u0042\u0079\u0074\u0065\u0073\u0041\u006C\u006C']();_0xd22a=130593^130599;console['\u006C\u006F\u0067']("\u5EA6\u957Fsetyblla".split("").reverse().join(""),allBytes['\u006C\u0065\u006E\u0067\u0074\u0068']);for(var n=110074^110074;n<imgTotalCount;n++){that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']++;for(var i=292849^292849;i<bufferCountImage;i++){let _btBuf=new Uint8Array(_bufLength);pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();pr['\u0073\u0065\u0074\u0043\u0075\u0074'](129585^129585);pr['\u0073\u0065\u0074\u004E\u006F\u0064\u0075'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u006E\u0073\u0069\u0074\u0079']);pr['\u0073\u0065\u0074\u004D\u0061\u0074'](324888^324889);pr['\u0073\u0065\u0074\u0053\u0061\u0076\u0065\u0070\u0061\u0070\u0065\u0072'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0053\u0061\u0076\u0065\u0050\u0061\u0070\u0065\u0072']);pr['\u0073\u0065\u0074\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074'](that['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']);if(i==(752835^752835)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0053\u0074'](950353^950352);}var _0xa_0xb35=(628621^628616)+(785139^785146);let bufferColumnCnt=637677^637677;_0xa_0xb35=586610^586615;if(i==bufferCountImage-(642341^642340)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0045\u006E\u0064'](635075^635074);if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']==bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()&&isEndFlag){pr['\u0073\u0065\u0074\u0050\u0072\u0074\u0045\u006E\u0064'](626290^626291);}bufferColumnCnt=_nColumnTotalCnt-nMax*i;}else{bufferColumnCnt=nMax;}var _0xe_0x2ea=(100849^100855)+(619620^619619);let star=nMax*i*nBytePerLine+marginleft*nBytePerLine;_0xe_0x2ea=640781^640778;var _0xd1ee2d=(903781^903789)+(165534^165530);let end=star+bufferColumnCnt*nBytePerLine;_0xd1ee2d='\u0069\u006F\u006B\u0062\u0063\u006A';let b=allBytes['\u0073\u006C\u0069\u0063\u0065'](star,end);_btBuf[772438^772434]=bufferColumnCnt&(782328^782087);_btBuf[895044^895041]=bufferColumnCnt>>(605328^605336)&(429271^429096);for(var y=356059^356059;y<b['\u006C\u0065\u006E\u0067\u0074\u0068'];y++){_btBuf[(436436^436442)+y]=b[y];}let btdata=pr['\u0074\u006F\u0042\u0079\u0074\u0065\u0041\u0072\u0072\u0061\u0079'](123151^123145);for(var z=567629^567631;z<(538154^538158);z++){_btBuf[z]=btdata[z-(809174^809172)];}if(marginleft<(225035^225034)){marginleft=444381^444380;}if(marginleft>(960920^960782)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']){marginleft=(467752^467902)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];}if(marginright<(305053^305052)){marginright=379872^379873;}if(marginright>(922018^921908)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']){marginright=(551433^551583)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];}_btBuf[739439^739431]=marginleft&(761841^761614);_btBuf[251546^251539]=marginleft>>(146237^146229)&(978913^978718);_btBuf[753218^753224]=marginright&(758891^758932);_btBuf[672091^672080]=marginright>>(978955^978947)&(613167^613328);_btBuf[503028^503026]=nBytePerLine&(706160^706191);_btBuf[390709^390713]=566153^566153;_btBuf[326863^326850]=624118^624118;var _0x939egb=(763634^763643)+(567956^567955);let len=_btBuf[725478^725475];_0x939egb='\u006B\u0064\u0063\u0065\u0064\u0067';len<<=591156^591164;len+=_btBuf[815007^815003];len*=_btBuf[232956^232954];len+=665623^665625;var _0x9158a=(965572^965574)+(476279^476272);let un=705089^705089;_0x9158a=717012^717014;for(var j=597174^597172;j<(791446^791448);j++){un+=_btBuf[j];}let x=Math['\u0066\u006C\u006F\u006F\u0072'](len/(138150^137894));if(x>(660043^660043)){for(var k=391285^391285;k<x;k++){un+=_btBuf[(k+(569741^569740))*(594951^595207)-(615791^615790)];}}_btBuf[575235^575235]=un;_btBuf[499948^499949]=un>>(596104^596096);countBuff['\u0070\u0075\u0073\u0068'](_btBuf);console['\u006C\u006F\u0067']("\u0070\u0072",pr);}}while(bufferTransferCount<bufferCountImage*num&&countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']>(790495^790495)){var _0x2_0xb6d=(831631^831631)+(888783^888777);let sendData=null;_0x2_0xb6d=(387649^387650)+(405900^405897);var _0x4985d=(305997^305994)+(573382^573383);let bufferCount=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);_0x4985d='\u0066\u0069\u0065\u0063\u0069\u0070';let bufferCountOne=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);do{let bufferPackage=Array();for(let a=501459^501459;a<bufferCount;a++){for(var b=711030^711030;b<_bufLength;b++){bufferPackage[b+a*_bufLength]=countBuff[a][b];}}try{this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=[];this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=bufferPackage;sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](bufferPackage);}catch(error){console['\u006C\u006F\u0067']("\u8D25\u5931\u7F29\u538B".split("").reverse().join(""));sendData=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];num=126301^126301;throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0034'],error);}bufferCount--;}while(sendData['\u004C\u0065\u006E\u0067\u0074\u0068']>_maxBufLength);bufferTransferCount=bufferTransferCount+bufferCount+(790391^790390);countBuff['\u0073\u0070\u006C\u0069\u0063\u0065'](725179^725179,bufferCountOne);if(sendData['\u006C\u0065\u006E\u0067\u0074\u0068']>(839118^839118)){that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](sendData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](sendData);}}}catch(error){throw error;}},async onDataProcessing(data){try{var _0x38644g=(676636^676634)+(966901^966900);const that=this;_0x38644g=717864^717869;var _0xaf03c=(563104^563112)+(326328^326330);let sendData=data;_0xaf03c=(202134^202129)+(694346^694347);let type=false;for(var i=865110^865111;i<(268301^268297);i++){let _0x0493e;let dataIndex3=sendData[i*(938243^938371)+(879915^879912)];_0x0493e=837413^837413;let _0xe3d;let dataIndex2=sendData[i*(566824^566952)+(730331^730329)];_0xe3d=(723556^723559)+(620452^620461);let _0x3707c;let dlen=dataIndex3<<(972001^972009)|dataIndex2;_0x3707c="hpanjh".split("").reverse().join("");dlen=dlen+(126533^126529);var _0x95g=(921471^921468)+(299687^299694);let opcode=sendData[i*(802651^802779)+(761886^761880)];_0x95g=(419874^419883)+(279348^279351);let dataIndex6=sendData[i*(540608^540480)+(345649^345652)];if(dlen>(360070^359942)&&(opcode==(281365^281518)||opcode==(937073^937079)||opcode==(335650^335845)||opcode==(765831^765824))&&dataIndex6==(690628^690542)){this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074'][i*(824589^824717)]=this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074'][i*(692394^692266)]+(382544^382545);type=!![];console['\u006C\u006F\u0067']("\u8FDB\u5165\u9519\u8BEF\u003A\u0020","\u91CD\u65B0\u538B\u7F29");}}if(type){sendData=[];console['\u006C\u006F\u0067']("\u518D\u6B21\u538B\u7F29");try{sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']);}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0034'],error);}}that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](sendData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](sendData);}catch(error){throw error;}},doPrint(){var _0xd79d6b=(245575^245574)+(186686^186681);const that=this;_0xd79d6b=(190352^190356)+(624282^624274);if(!that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']){that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=!![];if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']!=(671138^671138)){supVanPrintUtilsMP50['\u0064\u006F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']);}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0035'],"\u6253\u5370\u5B57\u6A21\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"));}}else{supVanPrintUtilsMP50['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0050\u0061\u0067\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'][593343^593343]);}},cleanImageDataListAll(){this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']=835629^835629;this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=false;},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;}};
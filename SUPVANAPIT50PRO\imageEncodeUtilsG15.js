import printingControl from"\u002E\u002F\u0050\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0043\u006F\u006E\u0074\u0072\u006F\u006C\u002E\u006A\u0073";import pr from"\u002E\u002F\u0050\u0041\u0047\u0045\u005F\u0052\u0045\u0047\u005F\u0042\u0049\u0054\u0053\u002E\u006A\u0073";import lzma from"\u002E\u002F\u006C\u007A\u006D\u0061\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import imageDataUtils from"\u002E\u002F\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0055\u0074\u0069\u006C\u0073";import bleToothManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065";import bleTool from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C";import constants from"\u002E\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";import supVanPrintUtilsG15 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";export default{'\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068':25,'\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068':2,'\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074':30,"dpiValue":8,"imageRgbaData":[],"encodeList":[],'\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065':0,'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],"imageDataListAllTotalCnt":0,"printFirstType":false,'\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074':[],"objectData":null,"printType":'',"callback":null,async initEncodeData(objectData,imageRgbaData){try{console['\u006C\u006F\u0067']("\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035",objectData);var _0x_0xbe2=(572376^572378)+(566363^566365);const that=this;_0x_0xbe2='\u006C\u0068\u0064\u0067\u006B\u0069';that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']=objectData;that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061']=imageRgbaData;that['\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065']=objectData['\u0069\u0073\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E']?593157^593156:400348^400348;that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();await that['\u0069\u006E\u0069\u0074\u0069\u0061\u006C\u0069\u007A\u0065\u0044\u0061\u0074\u0061']();await that['\u0069\u006E\u0069\u0074\u004C\u005A\u004D\u0041\u0044\u0061\u0074\u0061']();await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074']();}catch(error){throw error;}},async initializeData(){const that=this;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];printingControl['\u0063\u006C\u0065\u0061\u006E\u0050\u0063']();imageDataUtils['\u0067\u0065\u0074\u0043\u006C\u0065\u0061\u006E']();pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();let _0x0d4d4d;let length=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068'];_0x0d4d4d='\u006E\u0071\u0068\u0070\u0069\u006E';that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']=length-(756581^756577);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(108810^108809)){that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']=length-that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']*(238879^238877);}console['\u006C\u006F\u0067']("\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068",that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']==(996533^996533)){that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']=484057^484056;}let _0xdf66e;let rotateAngle=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']-(409680^409682))*(279562^279632);_0xdf66e=513316^513313;let object={'\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061':that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061'],'\u0057\u0069\u0064\u0074\u0068':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"Height":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"rotateAngle":rotateAngle,'\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']*(749919^749915),'\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D']*(653455^653451)+1.5*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"overturnType":that['\u006F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065'],"DeviceSn":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']};imageDataUtils['\u0067\u0065\u0074\u0041\u006C\u006C\u0042\u0079\u0074\u0065\u0073'](object);that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']=imageDataUtils['\u0067\u0065\u0074\u0057\u0069\u0064\u0074\u0068']();that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']=imageDataUtils['\u0067\u0065\u0074\u0048\u0065\u0069\u0067\u0068\u0074']();printingControl['\u0043\u006F\u006C\u0075\u006D\u006E\u004C\u0065\u0066\u0074']=that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},async initLZMAData(){try{const that=this;let _0xa8b95b;let _bufLength=580425^576329;_0xa8b95b='\u0062\u006C\u006C\u0071\u0063\u0066';let isEndFlag=!![];let imgTotalCount=564191^564190;var _0x64b60e=(967446^967442)+(117257^117257);let marginleft=imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']();_0x64b60e='\u0066\u006D\u0065\u006F\u0070\u0071';var _0xba4ca=(749147^749147)+(888105^888108);let marginright=imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']();_0xba4ca=(111324^111320)+(561864^561871);console['\u006C\u006F\u0067']("\u56FE\u7247\u5DE6\u8FB9\u8DDD",marginleft);var _0x37a29e=(785964^785962)+(636175^636168);let _nColumnTotalCnt=that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginleft;_0x37a29e=(540725^540732)+(275417^275418);console['\u006C\u006F\u0067']("\u6570\u5217\u7247\u56FE".split("").reverse().join(""),_nColumnTotalCnt);let _0xa1a;let nBytePerLine=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(217719^217712))/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);_0xa1a=(743973^743975)+(478698^478690);console['\u006C\u006F\u0067']("\u6570\u8282\u5B57\u5217\u6BCF".split("").reverse().join(""),nBytePerLine);let nMax=Math['\u0066\u006C\u006F\u006F\u0072']((_bufLength-(197603^197621))/nBytePerLine);console['\u006C\u006F\u0067']("\u6570\u5217\u5927\u6700\u533A\u51B2\u7F13\u4E2A\u6BCF".split("").reverse().join(""),nMax);var _0xe55g7a=(875525^875525)+(802339^802336);let bufferCountImage=Math['\u0066\u006C\u006F\u006F\u0072']((_nColumnTotalCnt+nMax-(649472^649473))/nMax);_0xe55g7a=(949396^949392)+(925588^925596);console['\u006C\u006F\u0067']("\u6BCF\u4E2A\u56FE\u7247\u7F13\u51B2\u533A\u6570\u91CF\u0020\u0062\u0075\u0066\u0066\u0065\u0072\u0043\u006F\u0075\u006E\u0074\u0049\u006D\u0061\u0067\u0065",bufferCountImage);let _0x5b9d;let allBytes=imageDataUtils['\u0067\u0065\u0074\u0042\u0079\u0074\u0065\u0073\u0041\u006C\u006C']();_0x5b9d='\u0064\u0067\u006A\u0069\u0064\u006D';console['\u006C\u006F\u0067']("\u5EA6\u957Fsetyblla".split("").reverse().join(""),allBytes['\u006C\u0065\u006E\u0067\u0074\u0068']);for(var n=202720^202720;n<imgTotalCount;n++){that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']++;for(var i=812171^812171;i<bufferCountImage;i++){let _btBuf=new Uint8Array(_bufLength);pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();pr['\u0073\u0065\u0074\u0043\u0075\u0074'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0043\u0075\u0074\u0054\u0079\u0070\u0065']);pr['\u0073\u0065\u0074\u004E\u006F\u0064\u0075'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u006E\u0073\u0069\u0074\u0079']);pr['\u0073\u0065\u0074\u004D\u0061\u0074'](993964^993965);pr['\u0073\u0065\u0074\u0053\u0061\u0076\u0065\u0070\u0061\u0070\u0065\u0072'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0053\u0061\u0076\u0065\u0050\u0061\u0070\u0065\u0072']?273325^273324:861903^861903);pr['\u0073\u0065\u0074\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']!==null&&that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']!==undefined?that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']:764232^764232);if(i==(646722^646722)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0053\u0074'](762145^762144);}let _0x2_0xda8;let bufferColumnCnt=113361^113361;_0x2_0xda8=(790147^790149)+(705754^705747);if(i==bufferCountImage-(576212^576213)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0045\u006E\u0064'](630382^630383);if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']==bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()&&isEndFlag){pr['\u0073\u0065\u0074\u0050\u0072\u0074\u0045\u006E\u0064'](808092^808093);}bufferColumnCnt=_nColumnTotalCnt-nMax*i;}else{bufferColumnCnt=nMax;}let _0xd512dc;let star=nMax*i*nBytePerLine+marginleft*nBytePerLine;_0xd512dc=(168068^168076)+(355777^355778);var _0xfab5cg=(170466^170464)+(959676^959679);let end=star+bufferColumnCnt*nBytePerLine;_0xfab5cg=(381219^381222)+(760383^760381);var _0x0a_0x072=(881706^881708)+(366535^366528);let b=allBytes['\u0073\u006C\u0069\u0063\u0065'](star,end);_0x0a_0x072='\u0066\u006D\u006E\u0068\u0070\u006E';_btBuf[889731^889735]=bufferColumnCnt&(376390^376505);_btBuf[807475^807478]=bufferColumnCnt>>(338189^338181)&(394203^394020);for(var y=453399^453399;y<b['\u006C\u0065\u006E\u0067\u0074\u0068'];y++){_btBuf[(425456^425470)+y]=b[y];}let btdata=pr['\u0074\u006F\u0042\u0079\u0074\u0065\u0041\u0072\u0072\u0061\u0079'](644911^644905);for(var z=405325^405327;z<(498623^498619);z++){_btBuf[z]=btdata[z-(127561^127563)];}_btBuf[624460^624458]=nBytePerLine&(873109^873066);let left=268777^268777;if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']!=(999975^999975)){left=906583^906582;}if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(968710^968709)){let marginLeft=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']+left)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];var _0x510ae=(707002^706994)+(557466^557470);let marginRight=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0x510ae=(210257^210260)+(155342^155340);_btBuf[904045^904037]=marginLeft&(199954^200173);_btBuf[161284^161293]=marginLeft>>(142460^142452)&(681537^681662);_btBuf[147265^147275]=marginRight&(989145^988966);_btBuf[153674^153665]=marginRight>>(721154^721162)&(838749^838818);}else{let marginLeft=((that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']+(651793^651797))*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'])/(351351^351349)+left*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];var _0xa62e6b=(684499^684502)+(125603^125610);let marginRight=(that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']+(242014^242010))*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginLeft;_0xa62e6b=(365627^365619)+(349130^349134);let _0x935ff;let margin=Math['\u006D\u0061\u0078'](944636^944632,marginLeft);_0x935ff=(148357^148357)+(335488^335497);_btBuf[257118^257110]=margin&(472358^472537);_btBuf[542823^542830]=margin>>(418289^418297)&(625276^625283);margin=Math['\u006D\u0061\u0078'](212772^212768,marginRight);_btBuf[863114^863104]=margin&(258731^258644);_btBuf[615234^615241]=margin>>(929113^929105)&(454886^454681);}_btBuf[194989^194977]=665587^665587;_btBuf[192937^192932]=594857^594857;let _0xcedd;let len=_btBuf[155487^155482];_0xcedd="qbnech".split("").reverse().join("");len<<=256693^256701;len+=_btBuf[636674^636678];len*=_btBuf[261208^261214];len+=255355^255349;let _0x6226bb;let un=542070^542070;_0x6226bb='\u0070\u006F\u0069\u006E\u0064\u006F';for(var j=652881^652883;j<(194927^194913);j++){un+=_btBuf[j];}let x=Math['\u0066\u006C\u006F\u006F\u0072'](len/(469937^469681));if(x>(259714^259714)){for(var k=791175^791175;k<x;k++){un+=_btBuf[(k+(239244^239245))*(631844^632100)-(950779^950778)];}}_btBuf[473306^473306]=un;_btBuf[164168^164169]=un>>(898187^898179);var _0xb791d=(206514^206518)+(421307^421305);let sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](_btBuf);_0xb791d=(370359^370355)+(820909^820905);if(sendData){that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](sendData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](sendData);}console['\u006C\u006F\u0067']("rp".split("").reverse().join(""),pr);}}}catch(error){throw error;}},async doPrint(){const that=this;console['\u006C\u006F\u0067']("\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u957F\u5EA6",that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']);console['\u006C\u006F\u0067']("\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065\u002E\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D\u0028\u0029\u957F\u5EA6",bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']());if(!that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']){that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=!![];if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']!=(964434^964434)){supVanPrintUtilsG15['\u0064\u006F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']);}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0035'],"\u6253\u5370\u5B57\u6A21\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"));}}else{supVanPrintUtilsG15['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0050\u0061\u0067\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']);}},cleanImageDataListAll(){this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']=295484^295484;this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=false;},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;}};